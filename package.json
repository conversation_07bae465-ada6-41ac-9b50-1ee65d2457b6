{"name": "adc-shop-merchants", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 4000", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern='__tests__'", "test:integration": "jest --testPathPattern='integration'", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:tables": "jest --testPathPattern='tables'", "test:dashboard": "jest --testPathPattern='dashboard'", "test:menu": "jest --testPathPattern='menu'", "test:orders": "jest --testPathPattern='orders'", "test:staff": "jest --testPathPattern='staff'", "test:restaurant": "jest --testPathPattern='restaurant'", "test:all": "npm run test && npm run test:e2e", "test:ci": "jest --coverage --watchAll=false && playwright test"}, "dependencies": {"@getbrevo/brevo": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@reduxjs/toolkit": "^2.8.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.20.5", "@types/qrcode": "^1.5.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "form-data": "^4.0.2", "framer-motion": "^12.15.0", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.509.0", "next": "15.3.2", "next-auth": "^4.24.10", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "twilio": "^5.6.0", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.8.4", "node-fetch": "^3.3.2", "prisma": "^6.7.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}