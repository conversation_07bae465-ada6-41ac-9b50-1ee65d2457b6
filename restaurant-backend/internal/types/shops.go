package types

import (
	"time"

	"github.com/google/uuid"
)

// Shop request/response types
type CreateShopRequest struct {
	Name          string             `json:"name" binding:"required"`
	Slug          string             `json:"slug" binding:"required"`
	Description   string             `json:"description"`
	ShopType      string             `json:"shop_type" binding:"required"`
	Email         string             `json:"email" binding:"omitempty,email"`
	Phone         string             `json:"phone"`
	Website       string             `json:"website"`
	Logo          string             `json:"logo"`
	CoverImage    string             `json:"cover_image"`
	Address       AddressRequest     `json:"address"`
	CuisineType   string             `json:"cuisine_type"`
	PriceRange    string             `json:"price_range"`
	SocialMedia   SocialMediaRequest `json:"social_media"`
	BusinessHours map[string]string  `json:"business_hours"`
}

type UpdateShopRequest struct {
	Name          *string             `json:"name"`
	Description   *string             `json:"description"`
	ShopType      *string             `json:"shop_type"`
	Email         *string             `json:"email" binding:"omitempty,email"`
	Phone         *string             `json:"phone"`
	Website       *string             `json:"website"`
	Logo          *string             `json:"logo"`
	CoverImage    *string             `json:"cover_image"`
	Address       *AddressRequest     `json:"address"`
	CuisineType   *string             `json:"cuisine_type"`
	PriceRange    *string             `json:"price_range"`
	SocialMedia   *SocialMediaRequest `json:"social_media"`
	BusinessHours *map[string]string  `json:"business_hours"`
	Status        *string             `json:"status"`
	IsActive      *bool               `json:"is_active"`
}

type ShopFilters struct {
	ShopType    string `form:"shop_type"`
	CuisineType string `form:"cuisine_type"`
	PriceRange  string `form:"price_range"`
	City        string `form:"city"`
	State       string `form:"state"`
	Status      string `form:"status"`
	IsActive    *bool  `form:"is_active"`
	Search      string `form:"search"`
	Page        int    `form:"page,default=1" binding:"omitempty,min=1"`
	Limit       int    `form:"limit,default=20" binding:"omitempty,min=1,max=100"`
}

// Branch request/response types
type CreateBranchRequest struct {
	Name          string            `json:"name" binding:"required"`
	Slug          string            `json:"slug" binding:"required"`
	Email         string            `json:"email" binding:"omitempty,email"`
	Phone         string            `json:"phone"`
	Address       AddressRequest    `json:"address"`
	BusinessHours map[string]string `json:"business_hours"`
	Timezone      string            `json:"timezone"`
}

type UpdateBranchRequest struct {
	Name          *string            `json:"name"`
	Email         *string            `json:"email" binding:"omitempty,email"`
	Phone         *string            `json:"phone"`
	Address       *AddressRequest    `json:"address"`
	BusinessHours *map[string]string `json:"business_hours"`
	Timezone      *string            `json:"timezone"`
	Status        *string            `json:"status"`
	IsActive      *bool              `json:"is_active"`
}

type BranchFilters struct {
	ShopID   uuid.UUID `form:"shop_id"`
	Status   string    `form:"status"`
	IsActive *bool     `form:"is_active"`
	City     string    `form:"city"`
	State    string    `form:"state"`
	Search   string    `form:"search"`
	Page     int       `form:"page,default=1" binding:"omitempty,min=1"`
	Limit    int       `form:"limit,default=20" binding:"omitempty,min=1,max=100"`
}

// Supporting types
type AddressRequest struct {
	Street  string `json:"street"`
	City    string `json:"city"`
	State   string `json:"state"`
	ZipCode string `json:"zip_code"`
	Country string `json:"country"`
}

type SocialMediaRequest struct {
	Facebook  string `json:"facebook"`
	Instagram string `json:"instagram"`
	Twitter   string `json:"twitter"`
	LinkedIn  string `json:"linkedin"`
	YouTube   string `json:"youtube"`
	TikTok    string `json:"tiktok"`
}

type BranchSettingsRequest struct {
	Currency             *string            `json:"currency"`
	TaxRate              *float64           `json:"tax_rate"`
	ServiceChargeRate    *float64           `json:"service_charge_rate"`
	DefaultTipPercentage *float64           `json:"default_tip_percentage"`
	PaymentMethods       *[]string          `json:"payment_methods"`
	Features             *map[string]bool   `json:"features"`
	Notifications        *map[string]bool   `json:"notifications"`
	Theme                *map[string]string `json:"theme"`
	OnlineOrdering       *bool              `json:"online_ordering"`
	TableReservations    *bool              `json:"table_reservations"`
	DeliveryEnabled      *bool              `json:"delivery_enabled"`
	PickupEnabled        *bool              `json:"pickup_enabled"`
	MaxTableCapacity     *int               `json:"max_table_capacity"`
	ReservationWindow    *int               `json:"reservation_window"`
	MinReservationTime   *int               `json:"min_reservation_time"`
}

// Response types
type ShopsResponse struct {
	Data       []ShopResponse `json:"data"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

type ShopResponse struct {
	ID            uuid.UUID           `json:"id"`
	OwnerID       uuid.UUID           `json:"owner_id"`
	Name          string              `json:"name"`
	Slug          string              `json:"slug"`
	Description   string              `json:"description"`
	ShopType      string              `json:"shop_type"`
	Email         string              `json:"email"`
	Phone         string              `json:"phone"`
	Website       string              `json:"website"`
	Logo          string              `json:"logo"`
	CoverImage    string              `json:"cover_image"`
	Address       AddressResponse     `json:"address"`
	CuisineType   string              `json:"cuisine_type"`
	PriceRange    string              `json:"price_range"`
	Rating        float64             `json:"rating"`
	ReviewCount   int                 `json:"review_count"`
	SocialMedia   SocialMediaResponse `json:"social_media"`
	BusinessHours map[string]string   `json:"business_hours"`
	Status        string              `json:"status"`
	IsVerified    bool                `json:"is_verified"`
	IsActive      bool                `json:"is_active"`
	CreatedAt     time.Time           `json:"created_at"`
	UpdatedAt     time.Time           `json:"updated_at"`
	Branches      []BranchResponse    `json:"branches,omitempty"`
}

type BranchResponse struct {
	ID            uuid.UUID         `json:"id"`
	ShopID        uuid.UUID         `json:"shop_id"`
	Name          string            `json:"name"`
	Slug          string            `json:"slug"`
	Email         string            `json:"email"`
	Phone         string            `json:"phone"`
	Address       AddressResponse   `json:"address"`
	BusinessHours map[string]string `json:"business_hours"`
	Timezone      string            `json:"timezone"`
	Status        string            `json:"status"`
	IsActive      bool              `json:"is_active"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
	Shop          *ShopResponse     `json:"shop,omitempty"`
}

type BranchesResponse struct {
	Data       []BranchResponse `json:"data"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	Limit      int              `json:"limit"`
	TotalPages int              `json:"total_pages"`
}

type AddressResponse struct {
	Street  string `json:"street"`
	City    string `json:"city"`
	State   string `json:"state"`
	ZipCode string `json:"zip_code"`
	Country string `json:"country"`
}

type SocialMediaResponse struct {
	Facebook  string `json:"facebook"`
	Instagram string `json:"instagram"`
	Twitter   string `json:"twitter"`
	LinkedIn  string `json:"linkedin"`
	YouTube   string `json:"youtube"`
	TikTok    string `json:"tiktok"`
}

type BranchSettingsResponse struct {
	Currency             string            `json:"currency"`
	TaxRate              float64           `json:"tax_rate"`
	ServiceChargeRate    float64           `json:"service_charge_rate"`
	DefaultTipPercentage float64           `json:"default_tip_percentage"`
	PaymentMethods       []string          `json:"payment_methods"`
	Features             map[string]bool   `json:"features"`
	Notifications        map[string]bool   `json:"notifications"`
	Theme                map[string]string `json:"theme"`
	OnlineOrdering       bool              `json:"online_ordering"`
	TableReservations    bool              `json:"table_reservations"`
	DeliveryEnabled      bool              `json:"delivery_enabled"`
	PickupEnabled        bool              `json:"pickup_enabled"`
	MaxTableCapacity     int               `json:"max_table_capacity"`
	ReservationWindow    int               `json:"reservation_window"`
	MinReservationTime   int               `json:"min_reservation_time"`
}

// Combined response types
type ShopWithBranchesResponse struct {
	ShopResponse
	Branches []BranchResponse `json:"branches"`
}

type BranchWithShopResponse struct {
	BranchResponse
	Shop ShopResponse `json:"shop"`
}
