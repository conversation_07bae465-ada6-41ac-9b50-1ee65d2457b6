package types

import (
	"time"

	"github.com/google/uuid"
)

// ReservationFilters represents filters for reservation queries
type ReservationFilters struct {
	Status    string     `form:"status" binding:"omitempty"`
	Date      string     `form:"date" binding:"omitempty"`
	StartDate string     `form:"start_date" binding:"omitempty"`
	EndDate   string     `form:"end_date" binding:"omitempty"`
	TableID   *uuid.UUID `form:"table_id" binding:"omitempty"`
	Search    string     `form:"search" binding:"omitempty"`
	Page      int        `form:"page" binding:"min=0"`
	Limit     int        `form:"limit" binding:"min=0"`
}

// ReservationResponse represents a reservation in API responses
type ReservationResponse struct {
	ID                 uuid.UUID `json:"id"`
	BranchID           uuid.UUID `json:"branch_id"`
	CustomerName       string    `json:"customer_name"`
	CustomerPhone      string    `json:"customer_phone"`
	CustomerEmail      string    `json:"customer_email"`
	PartySize          int       `json:"party_size"`
	ReservationDate    time.Time `json:"reservation_date"`
	ReservationTime    time.Time `json:"reservation_time"`
	Duration           int       `json:"duration"`
	TableID            *uuid.UUID `json:"table_id"`
	TableName          string    `json:"table_name,omitempty"`
	Status             string    `json:"status"`
	SpecialRequests    string    `json:"special_requests"`
	Notes              string    `json:"notes"`
	Source             string    `json:"source"`
	CheckedInAt        *time.Time `json:"checked_in_at"`
	CompletedAt        *time.Time `json:"completed_at"`
	CancelledAt        *time.Time `json:"cancelled_at"`
	CancellationReason string    `json:"cancellation_reason"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// ReservationsResponse represents paginated reservations response
type ReservationsResponse struct {
	Data       []ReservationResponse `json:"data"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	Limit      int                   `json:"limit"`
	TotalPages int                   `json:"total_pages"`
}

// CreateReservationRequest represents a request to create a reservation
type CreateReservationRequest struct {
	CustomerName       string     `json:"customer_name" binding:"required"`
	CustomerPhone      string     `json:"customer_phone" binding:"required"`
	CustomerEmail      string     `json:"customer_email" binding:"omitempty,email"`
	PartySize          int        `json:"party_size" binding:"required,min=1"`
	ReservationDate    string     `json:"reservation_date" binding:"required"`
	ReservationTime    string     `json:"reservation_time" binding:"required"`
	Duration           int        `json:"duration" binding:"omitempty,min=30"`
	TableID            *uuid.UUID `json:"table_id" binding:"omitempty"`
	SpecialRequests    string     `json:"special_requests" binding:"omitempty"`
	Notes              string     `json:"notes" binding:"omitempty"`
	Source             string     `json:"source" binding:"omitempty"`
}

// UpdateReservationRequest represents a request to update a reservation
type UpdateReservationRequest struct {
	CustomerName       string     `json:"customer_name" binding:"omitempty"`
	CustomerPhone      string     `json:"customer_phone" binding:"omitempty"`
	CustomerEmail      string     `json:"customer_email" binding:"omitempty,email"`
	PartySize          int        `json:"party_size" binding:"omitempty,min=1"`
	ReservationDate    string     `json:"reservation_date" binding:"omitempty"`
	ReservationTime    string     `json:"reservation_time" binding:"omitempty"`
	Duration           int        `json:"duration" binding:"omitempty,min=30"`
	TableID            *uuid.UUID `json:"table_id" binding:"omitempty"`
	Status             string     `json:"status" binding:"omitempty"`
	SpecialRequests    string     `json:"special_requests" binding:"omitempty"`
	Notes              string     `json:"notes" binding:"omitempty"`
	CancellationReason string     `json:"cancellation_reason" binding:"omitempty"`
}

// TimeSlot represents an available time slot
type TimeSlot struct {
	Time      string `json:"time"`
	Available bool   `json:"available"`
}

// AvailabilityResponse represents availability for a specific date
type AvailabilityResponse struct {
	Date      string     `json:"date"`
	TimeSlots []TimeSlot `json:"time_slots"`
}

// ReservationStatusRequest represents a request to update reservation status
type ReservationStatusRequest struct {
	Status             string `json:"status" binding:"required"`
	CancellationReason string `json:"cancellation_reason" binding:"omitempty"`
}

// Reservation status constants
const (
	ReservationStatusPending   = "pending"
	ReservationStatusConfirmed = "confirmed"
	ReservationStatusSeated    = "seated"
	ReservationStatusCompleted = "completed"
	ReservationStatusCancelled = "cancelled"
	ReservationStatusNoShow    = "no-show"
)

// Reservation source constants
const (
	ReservationSourcePhone   = "phone"
	ReservationSourceWebsite = "website"
	ReservationSourceWalkIn  = "walk-in"
	ReservationSourceApp     = "app"
)
