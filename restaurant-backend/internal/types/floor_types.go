package types

import (
	"restaurant-backend/internal/models"
)

// Floor request types
type CreateFloorRequest struct {
	Name        string                `json:"name" binding:"required,min=1,max=255"`
	Description string                `json:"description"`
	Order       int                   `json:"order" binding:"required,min=1"`
	Layout      *models.FloorLayout   `json:"layout"`
}

type UpdateFloorRequest struct {
	Name        string                `json:"name" binding:"omitempty,min=1,max=255"`
	Description *string               `json:"description"`
	Order       int                   `json:"order" binding:"omitempty,min=1"`
	Layout      *models.FloorLayout   `json:"layout"`
}

// Floor filter types
type FloorFilters struct {
	Name         string `form:"name"`
	IncludeAreas bool   `form:"include_areas"`
}

// Floor response types
type FloorResponse struct {
	ID          string                `json:"id"`
	BranchID    string                `json:"branch_id"`
	Name        string                `json:"name"`
	Description string                `json:"description"`
	Order       int                   `json:"order"`
	Layout      models.FloorLayout    `json:"layout"`
	IsActive    bool                  `json:"is_active"`
	CreatedAt   string                `json:"created_at"`
	UpdatedAt   string                `json:"updated_at"`
	Areas       []AreaResponse        `json:"areas,omitempty"`
}

type FloorListResponse struct {
	Floors []FloorResponse `json:"floors"`
	Total  int             `json:"total"`
}
