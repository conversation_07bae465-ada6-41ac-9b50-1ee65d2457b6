package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// InventoryService handles inventory management business logic
type InventoryService struct {
	inventoryRepo repositories.InventoryRepository
	logger        *logrus.Logger
}

// NewInventoryService creates a new inventory service
func NewInventoryService(inventoryRepo repositories.InventoryRepository, logger *logrus.Logger) *InventoryService {
	return &InventoryService{
		inventoryRepo: inventoryRepo,
		logger:        logger,
	}
}

// InventoryDashboard represents inventory dashboard data
type InventoryDashboard struct {
	TotalItems      int                    `json:"total_items"`
	LowStockItems   int                    `json:"low_stock_items"`
	ExpiringItems   int                    `json:"expiring_items"`
	TotalValue      float64                `json:"total_value"`
	RecentMovements []StockMovementSummary `json:"recent_movements"`
	LowStockAlerts  []LowStockAlert        `json:"low_stock_alerts"`
	ExpiryAlerts    []ExpiryAlert          `json:"expiry_alerts"`
	TopIngredients  []types.IngredientUsage      `json:"top_ingredients"`
}

// StockMovementSummary represents a summary of stock movements
type StockMovementSummary struct {
	ID           uuid.UUID `json:"id"`
	IngredientName string  `json:"ingredient_name"`
	MovementType string    `json:"movement_type"`
	Quantity     float64   `json:"quantity"`
	Unit         string    `json:"unit"`
	Reason       string    `json:"reason"`
	CreatedAt    time.Time `json:"created_at"`
}

// LowStockAlert represents a low stock alert
type LowStockAlert struct {
	IngredientID   uuid.UUID `json:"ingredient_id"`
	IngredientName string    `json:"ingredient_name"`
	CurrentStock   float64   `json:"current_stock"`
	MinStockLevel  float64   `json:"min_stock_level"`
	Unit           string    `json:"unit"`
	Severity       string    `json:"severity"`
}

// ExpiryAlert represents an expiry alert
type ExpiryAlert struct {
	InventoryItemID uuid.UUID  `json:"inventory_item_id"`
	IngredientName  string     `json:"ingredient_name"`
	CurrentStock    float64    `json:"current_stock"`
	ExpiryDate      *time.Time `json:"expiry_date"`
	DaysUntilExpiry int        `json:"days_until_expiry"`
	Unit            string     `json:"unit"`
	Severity        string     `json:"severity"`
}



// CreateIngredientRequest represents a request to create an ingredient
type CreateIngredientRequest struct {
	Name            string                 `json:"name" binding:"required"`
	Description     string                 `json:"description"`
	Category        string                 `json:"category" binding:"required"`
	Unit            string                 `json:"unit" binding:"required"`
	CostPerUnit     float64                `json:"cost_per_unit" binding:"required"`
	SKU             string                 `json:"sku"`
	Barcode         string                 `json:"barcode"`
	MinStockLevel   float64                `json:"min_stock_level" binding:"required"`
	MaxStockLevel   float64                `json:"max_stock_level" binding:"required"`
	ReorderPoint    float64                `json:"reorder_point" binding:"required"`
	ShelfLife       int                    `json:"shelf_life"`
	StorageTemp     string                 `json:"storage_temp"`
	IsPerishable    bool                   `json:"is_perishable"`
	SupplierID      *uuid.UUID             `json:"supplier_id"`
	AllergenInfo    []string               `json:"allergen_info"`
	NutritionalInfo map[string]interface{} `json:"nutritional_info"`
}

// UpdateStockRequest represents a request to update stock levels
type UpdateStockRequest struct {
	IngredientID  uuid.UUID `json:"ingredient_id" binding:"required"`
	MovementType  string    `json:"movement_type" binding:"required"`
	Quantity      float64   `json:"quantity" binding:"required"`
	Reason        string    `json:"reason" binding:"required"`
	Reference     string    `json:"reference"`
	ReferenceType string    `json:"reference_type"`
	CostPerUnit   float64   `json:"cost_per_unit"`
	ExpiryDate    *time.Time `json:"expiry_date"`
	BatchNumber   string    `json:"batch_number"`
	Location      string    `json:"location"`
	Notes         string    `json:"notes"`
}

// CreatePurchaseOrderRequest represents a request to create a purchase order
type CreatePurchaseOrderRequest struct {
	SupplierID     uuid.UUID                    `json:"supplier_id" binding:"required"`
	ExpectedDate   *time.Time                   `json:"expected_date"`
	Notes          string                       `json:"notes"`
	Items          []CreatePurchaseOrderItemRequest `json:"items" binding:"required,min=1"`
}

// CreatePurchaseOrderItemRequest represents an item in a purchase order
type CreatePurchaseOrderItemRequest struct {
	IngredientID uuid.UUID `json:"ingredient_id" binding:"required"`
	Quantity     float64   `json:"quantity" binding:"required"`
	UnitPrice    float64   `json:"unit_price" binding:"required"`
}

// GetInventoryDashboard retrieves inventory dashboard data
func (s *InventoryService) GetInventoryDashboard(ctx context.Context, branchID uuid.UUID) (*InventoryDashboard, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting inventory dashboard")

	// Get inventory items
	items, err := s.inventoryRepo.GetInventoryItems(ctx, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get inventory items: %w", err)
	}

	// Calculate dashboard metrics
	dashboard := &InventoryDashboard{
		TotalItems:      len(items),
		LowStockItems:   0,
		ExpiringItems:   0,
		TotalValue:      0,
		RecentMovements: []StockMovementSummary{},
		LowStockAlerts:  []LowStockAlert{},
		ExpiryAlerts:    []ExpiryAlert{},
		TopIngredients:  []types.IngredientUsage{},
	}

	// Process inventory items
	for _, item := range items {
		dashboard.TotalValue += item.TotalValue

		// Check for low stock
		if item.IsLowStock(&item.Ingredient) {
			dashboard.LowStockItems++
			severity := s.calculateStockSeverity(item.AvailableStock, item.Ingredient.MinStockLevel)
			dashboard.LowStockAlerts = append(dashboard.LowStockAlerts, LowStockAlert{
				IngredientID:   item.IngredientID,
				IngredientName: item.Ingredient.Name,
				CurrentStock:   item.AvailableStock,
				MinStockLevel:  item.Ingredient.MinStockLevel,
				Unit:           item.Ingredient.Unit,
				Severity:       severity,
			})
		}

		// Check for expiring items
		if item.IsExpiringSoon(7) { // 7 days
			dashboard.ExpiringItems++
			daysUntilExpiry := int(time.Until(*item.ExpiryDate).Hours() / 24)
			severity := s.calculateExpirySeverity(daysUntilExpiry)
			dashboard.ExpiryAlerts = append(dashboard.ExpiryAlerts, ExpiryAlert{
				InventoryItemID: item.ID,
				IngredientName:  item.Ingredient.Name,
				CurrentStock:    item.CurrentStock,
				ExpiryDate:      item.ExpiryDate,
				DaysUntilExpiry: daysUntilExpiry,
				Unit:            item.Ingredient.Unit,
				Severity:        severity,
			})
		}
	}

	// Get recent movements
	recentMovements, err := s.inventoryRepo.GetRecentStockMovements(ctx, branchID, 10)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get recent stock movements")
	} else {
		for _, movement := range recentMovements {
			dashboard.RecentMovements = append(dashboard.RecentMovements, StockMovementSummary{
				ID:             movement.ID,
				IngredientName: movement.Ingredient.Name,
				MovementType:   movement.MovementType,
				Quantity:       movement.Quantity,
				Unit:           movement.Unit,
				Reason:         movement.Reason,
				CreatedAt:      movement.CreatedAt,
			})
		}
	}

	// Get top ingredients by usage
	topIngredients, err := s.inventoryRepo.GetTopIngredientsByUsage(ctx, branchID, 10)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get top ingredients")
	} else {
		dashboard.TopIngredients = topIngredients
	}

	return dashboard, nil
}

// CreateIngredient creates a new ingredient
func (s *InventoryService) CreateIngredient(ctx context.Context, req CreateIngredientRequest) (*models.Ingredient, error) {
	s.logger.WithField("name", req.Name).Info("Creating ingredient")

	ingredient := &models.Ingredient{
		Name:            req.Name,
		Description:     req.Description,
		Category:        req.Category,
		Unit:            req.Unit,
		CostPerUnit:     req.CostPerUnit,
		SKU:             req.SKU,
		Barcode:         req.Barcode,
		MinStockLevel:   req.MinStockLevel,
		MaxStockLevel:   req.MaxStockLevel,
		ReorderPoint:    req.ReorderPoint,
		ShelfLife:       req.ShelfLife,
		StorageTemp:     req.StorageTemp,
		IsPerishable:    req.IsPerishable,
		SupplierID:      req.SupplierID,
		AllergenInfo:    models.AllergenData(req.AllergenInfo),
		NutritionalInfo: models.NutritionalData(req.NutritionalInfo),
		IsActive:        true,
	}

	if err := s.inventoryRepo.CreateIngredient(ctx, ingredient); err != nil {
		return nil, fmt.Errorf("failed to create ingredient: %w", err)
	}

	s.logger.WithField("ingredient_id", ingredient.ID).Info("Ingredient created successfully")
	return ingredient, nil
}

// UpdateStock updates stock levels for an ingredient
func (s *InventoryService) UpdateStock(ctx context.Context, branchID uuid.UUID, userID uuid.UUID, req UpdateStockRequest) (*models.InventoryItem, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id":     branchID,
		"ingredient_id": req.IngredientID,
		"movement_type": req.MovementType,
		"quantity":      req.Quantity,
	}).Info("Updating stock")

	// Get current inventory item
	inventoryItem, err := s.inventoryRepo.GetInventoryItem(ctx, branchID, req.IngredientID)
	if err != nil {
		return nil, fmt.Errorf("failed to get inventory item: %w", err)
	}

	// If inventory item doesn't exist, create it
	if inventoryItem == nil {
		_, err := s.inventoryRepo.GetIngredient(ctx, req.IngredientID)
		if err != nil {
			return nil, fmt.Errorf("failed to get ingredient: %w", err)
		}

		inventoryItem = &models.InventoryItem{
			BranchID:       branchID,
			IngredientID:   req.IngredientID,
			CurrentStock:   0,
			ReservedStock:  0,
			AvailableStock: 0,
			Status:         models.InventoryStatusAvailable,
			CostPerUnit:    req.CostPerUnit,
			Location:       req.Location,
		}

		if err := s.inventoryRepo.CreateInventoryItem(ctx, inventoryItem); err != nil {
			return nil, fmt.Errorf("failed to create inventory item: %w", err)
		}
	}

	// Record the previous stock level
	previousStock := inventoryItem.CurrentStock

	// Apply stock movement
	switch req.MovementType {
	case models.MovementTypeIn:
		inventoryItem.CurrentStock += req.Quantity
		if req.ExpiryDate != nil {
			inventoryItem.ExpiryDate = req.ExpiryDate
		}
		if req.BatchNumber != "" {
			inventoryItem.BatchNumber = req.BatchNumber
		}
	case models.MovementTypeOut:
		if inventoryItem.AvailableStock < req.Quantity {
			return nil, fmt.Errorf("insufficient stock: available %.2f, requested %.2f", inventoryItem.AvailableStock, req.Quantity)
		}
		inventoryItem.CurrentStock -= req.Quantity
	case models.MovementTypeAdjustment:
		inventoryItem.CurrentStock = req.Quantity
	case models.MovementTypeWaste:
		if inventoryItem.CurrentStock < req.Quantity {
			return nil, fmt.Errorf("insufficient stock for waste: available %.2f, requested %.2f", inventoryItem.CurrentStock, req.Quantity)
		}
		inventoryItem.CurrentStock -= req.Quantity
	default:
		return nil, fmt.Errorf("invalid movement type: %s", req.MovementType)
	}

	// Update calculated fields
	inventoryItem.CalculateAvailableStock()
	inventoryItem.CalculateTotalValue()
	inventoryItem.LastRestocked = &time.Time{}
	*inventoryItem.LastRestocked = time.Now()

	// Update inventory item
	if err := s.inventoryRepo.UpdateInventoryItem(ctx, inventoryItem); err != nil {
		return nil, fmt.Errorf("failed to update inventory item: %w", err)
	}

	// Record stock movement
	movement := &models.StockMovement{
		BranchID:        branchID,
		IngredientID:    req.IngredientID,
		MovementType:    req.MovementType,
		Quantity:        req.Quantity,
		Unit:            inventoryItem.Ingredient.Unit,
		Reason:          req.Reason,
		Reference:       req.Reference,
		ReferenceType:   req.ReferenceType,
		CostPerUnit:     req.CostPerUnit,
		PreviousStock:   previousStock,
		NewStock:        inventoryItem.CurrentStock,
		PerformedBy:     userID,
		Notes:           req.Notes,
	}
	movement.CalculateTotalCost()

	if err := s.inventoryRepo.CreateStockMovement(ctx, movement); err != nil {
		s.logger.WithError(err).Error("Failed to record stock movement")
		// Don't fail the operation, just log the error
	}

	// Record waste if applicable
	if req.MovementType == models.MovementTypeWaste {
		wasteRecord := &models.WasteRecord{
			BranchID:     branchID,
			IngredientID: req.IngredientID,
			Quantity:     req.Quantity,
			Unit:         inventoryItem.Ingredient.Unit,
			Reason:       req.Reason,
			Cost:         req.Quantity * req.CostPerUnit,
			RecordedBy:   userID,
			WasteDate:    time.Now(),
			Notes:        req.Notes,
		}

		if err := s.inventoryRepo.CreateWasteRecord(ctx, wasteRecord); err != nil {
			s.logger.WithError(err).Error("Failed to record waste")
		}
	}

	s.logger.WithField("inventory_item_id", inventoryItem.ID).Info("Stock updated successfully")
	return inventoryItem, nil
}

// CreatePurchaseOrder creates a new purchase order
func (s *InventoryService) CreatePurchaseOrder(ctx context.Context, branchID uuid.UUID, userID uuid.UUID, req CreatePurchaseOrderRequest) (*models.PurchaseOrder, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id":   branchID,
		"supplier_id": req.SupplierID,
		"items_count": len(req.Items),
	}).Info("Creating purchase order")

	// Create purchase order
	po := &models.PurchaseOrder{
		BranchID:     branchID,
		SupplierID:   req.SupplierID,
		Status:       models.POStatusPending,
		OrderDate:    time.Now(),
		ExpectedDate: req.ExpectedDate,
		Notes:        req.Notes,
		CreatedBy:    userID,
	}

	// Calculate totals
	var subtotal float64
	for _, itemReq := range req.Items {
		itemTotal := itemReq.Quantity * itemReq.UnitPrice
		subtotal += itemTotal

		item := models.PurchaseOrderItem{
			IngredientID: itemReq.IngredientID,
			Quantity:     itemReq.Quantity,
			UnitPrice:    itemReq.UnitPrice,
			Total:        itemTotal,
			Status:       models.POStatusPending,
		}
		po.Items = append(po.Items, item)
	}

	po.Subtotal = subtotal
	po.TaxAmount = subtotal * 0.08 // 8% tax
	po.CalculateTotal()

	// Create purchase order
	if err := s.inventoryRepo.CreatePurchaseOrder(ctx, po); err != nil {
		return nil, fmt.Errorf("failed to create purchase order: %w", err)
	}

	s.logger.WithField("purchase_order_id", po.ID).Info("Purchase order created successfully")
	return po, nil
}

// GetLowStockItems returns items that are below reorder point
func (s *InventoryService) GetLowStockItems(ctx context.Context, branchID uuid.UUID) ([]LowStockAlert, error) {
	s.logger.WithField("branch_id", branchID).Info("Getting low stock items")

	items, err := s.inventoryRepo.GetLowStockItems(ctx, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get low stock items: %w", err)
	}

	alerts := make([]LowStockAlert, len(items))
	for i, item := range items {
		alerts[i] = LowStockAlert{
			IngredientID:   item.IngredientID,
			IngredientName: item.Ingredient.Name,
			CurrentStock:   item.AvailableStock,
			MinStockLevel:  item.Ingredient.MinStockLevel,
			Unit:           item.Ingredient.Unit,
			Severity:       s.calculateStockSeverity(item.AvailableStock, item.Ingredient.MinStockLevel),
		}
	}

	return alerts, nil
}

// GetExpiringItems returns items that are expiring soon
func (s *InventoryService) GetExpiringItems(ctx context.Context, branchID uuid.UUID, days int) ([]ExpiryAlert, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id": branchID,
		"days":      days,
	}).Info("Getting expiring items")

	items, err := s.inventoryRepo.GetExpiringItems(ctx, branchID, days)
	if err != nil {
		return nil, fmt.Errorf("failed to get expiring items: %w", err)
	}

	alerts := make([]ExpiryAlert, len(items))
	for i, item := range items {
		daysUntilExpiry := int(time.Until(*item.ExpiryDate).Hours() / 24)
		alerts[i] = ExpiryAlert{
			InventoryItemID: item.ID,
			IngredientName:  item.Ingredient.Name,
			CurrentStock:    item.CurrentStock,
			ExpiryDate:      item.ExpiryDate,
			DaysUntilExpiry: daysUntilExpiry,
			Unit:            item.Ingredient.Unit,
			Severity:        s.calculateExpirySeverity(daysUntilExpiry),
		}
	}

	return alerts, nil
}

// Helper methods

func (s *InventoryService) calculateStockSeverity(currentStock, minStock float64) string {
	ratio := currentStock / minStock
	if ratio <= 0.25 {
		return "critical"
	} else if ratio <= 0.5 {
		return "high"
	} else if ratio <= 0.75 {
		return "medium"
	}
	return "low"
}

func (s *InventoryService) calculateExpirySeverity(daysUntilExpiry int) string {
	if daysUntilExpiry <= 1 {
		return "critical"
	} else if daysUntilExpiry <= 3 {
		return "high"
	} else if daysUntilExpiry <= 7 {
		return "medium"
	}
	return "low"
}
