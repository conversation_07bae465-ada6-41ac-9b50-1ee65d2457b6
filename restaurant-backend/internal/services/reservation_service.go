package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ReservationService handles reservation business logic
type ReservationService struct {
	reservationRepo repositories.ReservationRepository
	logger          *logrus.Logger
}

func NewReservationService(reservationRepo repositories.ReservationRepository, logger *logrus.Logger) *ReservationService {
	return &ReservationService{reservationRepo: reservationRepo, logger: logger}
}

// GetReservations retrieves reservations for a branch with filters
func (s *ReservationService) GetReservations(ctx context.Context, branchID uuid.UUID, filters types.ReservationFilters) (*types.ReservationsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	reservations, total, err := s.reservationRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservations")
		return nil, fmt.Errorf("failed to get reservations: %w", err)
	}

	// Convert to response format
	reservationResponses := make([]types.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		reservationResponses[i] = s.convertReservationToResponse(reservation)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ReservationsResponse{
		Data:       reservationResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

// GetReservation retrieves a single reservation by ID
func (s *ReservationService) GetReservation(ctx context.Context, reservationID uuid.UUID) (*types.ReservationResponse, error) {
	reservation, err := s.reservationRepo.GetByID(ctx, reservationID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// CreateReservation creates a new reservation
func (s *ReservationService) CreateReservation(ctx context.Context, branchID uuid.UUID, req types.CreateReservationRequest) (*types.ReservationResponse, error) {
	// Parse reservation date and time
	reservationDate, err := time.Parse("2006-01-02", req.ReservationDate)
	if err != nil {
		return nil, fmt.Errorf("invalid reservation date format: %w", err)
	}

	reservationTime, err := time.Parse("15:04", req.ReservationTime)
	if err != nil {
		return nil, fmt.Errorf("invalid reservation time format: %w", err)
	}

	// Set default duration if not provided
	duration := req.Duration
	if duration == 0 {
		duration = 120 // 2 hours default
	}

	// Set default source if not provided
	source := req.Source
	if source == "" {
		source = types.ReservationSourceWebsite
	}

	reservation := &models.Reservation{
		BranchID:        branchID,
		CustomerName:    req.CustomerName,
		CustomerPhone:   req.CustomerPhone,
		CustomerEmail:   req.CustomerEmail,
		PartySize:       req.PartySize,
		ReservationDate: reservationDate,
		ReservationTime: reservationTime,
		Duration:        duration,
		TableID:         req.TableID,
		Status:          types.ReservationStatusPending,
		SpecialRequests: req.SpecialRequests,
		Notes:           req.Notes,
		Source:          source,
	}

	if err := s.reservationRepo.Create(ctx, reservation); err != nil {
		s.logger.WithError(err).Error("Failed to create reservation")
		return nil, fmt.Errorf("failed to create reservation: %w", err)
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// UpdateReservation updates an existing reservation
func (s *ReservationService) UpdateReservation(ctx context.Context, reservationID uuid.UUID, req types.UpdateReservationRequest) (*types.ReservationResponse, error) {
	reservation, err := s.reservationRepo.GetByID(ctx, reservationID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get reservation for update")
		return nil, fmt.Errorf("failed to get reservation: %w", err)
	}

	if reservation == nil {
		return nil, fmt.Errorf("reservation not found")
	}

	// Update fields if provided
	if req.CustomerName != "" {
		reservation.CustomerName = req.CustomerName
	}
	if req.CustomerPhone != "" {
		reservation.CustomerPhone = req.CustomerPhone
	}
	if req.CustomerEmail != "" {
		reservation.CustomerEmail = req.CustomerEmail
	}
	if req.PartySize > 0 {
		reservation.PartySize = req.PartySize
	}
	if req.ReservationDate != "" {
		if date, err := time.Parse("2006-01-02", req.ReservationDate); err == nil {
			reservation.ReservationDate = date
		}
	}
	if req.ReservationTime != "" {
		if timeVal, err := time.Parse("15:04", req.ReservationTime); err == nil {
			reservation.ReservationTime = timeVal
		}
	}
	if req.Duration > 0 {
		reservation.Duration = req.Duration
	}
	if req.TableID != nil {
		reservation.TableID = req.TableID
	}
	if req.Status != "" {
		reservation.Status = req.Status
	}
	if req.SpecialRequests != "" {
		reservation.SpecialRequests = req.SpecialRequests
	}
	if req.Notes != "" {
		reservation.Notes = req.Notes
	}
	if req.CancellationReason != "" {
		reservation.CancellationReason = req.CancellationReason
	}

	if err := s.reservationRepo.Update(ctx, reservation); err != nil {
		s.logger.WithError(err).Error("Failed to update reservation")
		return nil, fmt.Errorf("failed to update reservation: %w", err)
	}

	response := s.convertReservationToResponse(*reservation)
	return &response, nil
}

// GetTodayReservations retrieves today's reservations for a branch
func (s *ReservationService) GetTodayReservations(ctx context.Context, branchID uuid.UUID) ([]types.ReservationResponse, error) {
	reservations, err := s.reservationRepo.GetTodayReservations(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get today's reservations")
		return nil, fmt.Errorf("failed to get today's reservations: %w", err)
	}

	// Convert to response format
	reservationResponses := make([]types.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		reservationResponses[i] = s.convertReservationToResponse(reservation)
	}

	return reservationResponses, nil
}

// GetAvailability retrieves available time slots for a date
func (s *ReservationService) GetAvailability(ctx context.Context, branchID uuid.UUID, date time.Time) (*types.AvailabilityResponse, error) {
	timeSlots, err := s.reservationRepo.GetAvailability(ctx, branchID, date)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get availability")
		return nil, fmt.Errorf("failed to get availability: %w", err)
	}

	return &types.AvailabilityResponse{
		Date:      date.Format("2006-01-02"),
		TimeSlots: timeSlots,
	}, nil
}

// Helper method to convert model to response
func (s *ReservationService) convertReservationToResponse(reservation models.Reservation) types.ReservationResponse {
	response := types.ReservationResponse{
		ID:                 reservation.ID,
		BranchID:           reservation.BranchID,
		CustomerName:       reservation.CustomerName,
		CustomerPhone:      reservation.CustomerPhone,
		CustomerEmail:      reservation.CustomerEmail,
		PartySize:          reservation.PartySize,
		ReservationDate:    reservation.ReservationDate,
		ReservationTime:    reservation.ReservationTime,
		Duration:           reservation.Duration,
		TableID:            reservation.TableID,
		Status:             reservation.Status,
		SpecialRequests:    reservation.SpecialRequests,
		Notes:              reservation.Notes,
		Source:             reservation.Source,
		CheckedInAt:        reservation.CheckedInAt,
		CompletedAt:        reservation.CompletedAt,
		CancelledAt:        reservation.CancelledAt,
		CancellationReason: reservation.CancellationReason,
		CreatedAt:          reservation.CreatedAt,
		UpdatedAt:          reservation.UpdatedAt,
	}

	// Add table name if table is loaded
	if reservation.Table != nil {
		response.TableName = reservation.Table.Name
	}

	return response
}
