package services

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// MenuService handles menu business logic
type MenuService struct {
	categoryRepo repositories.MenuCategoryRepository
	itemRepo     repositories.MenuItemRepository
	logger       *logrus.Logger
}

func NewMenuService(
	categoryRepo repositories.MenuCategoryRepository,
	itemRepo repositories.MenuItemRepository,
	logger *logrus.Logger,
) *MenuService {
	return &MenuService{
		categoryRepo: categoryRepo,
		itemRepo:     itemRepo,
		logger:       logger,
	}
}

// Category methods
func (s *MenuService) GetCategories(ctx context.Context, branchID uuid.UUID, filters types.CategoryFilters) (*types.CategoriesResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	categories, total, err := s.categoryRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get categories")
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	// Convert to response format
	categoryResponses := make([]types.CategoryResponse, len(categories))
	for i, category := range categories {
		categoryResponses[i] = s.convertCategoryToResponse(category)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.CategoriesResponse{
		Data:       categoryResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *MenuService) CreateCategory(ctx context.Context, branchID uuid.UUID, req types.CreateCategoryRequest) (*types.CategoryResponse, error) {
	category := &models.MenuCategory{
		BranchID:    branchID,
		Name:        req.Name,
		Description: req.Description,
		ImageURL:    req.ImageURL,
		SortOrder:   req.SortOrder,
		IsActive:    req.IsActive,
	}

	// Generate slug from name
	category.Slug = s.generateSlug(req.Name)

	if err := s.categoryRepo.Create(ctx, category); err != nil {
		s.logger.WithError(err).Error("Failed to create category")
		return nil, fmt.Errorf("failed to create category: %w", err)
	}

	// Reload category with relationships
	createdCategory, err := s.categoryRepo.GetByID(ctx, category.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get created category")
		return nil, fmt.Errorf("failed to get created category")
	}

	response := s.convertCategoryToResponse(*createdCategory)
	return &response, nil
}

func (s *MenuService) UpdateCategory(ctx context.Context, categoryID uuid.UUID, req types.UpdateCategoryRequest) (*types.CategoryResponse, error) {
	category, err := s.categoryRepo.GetByID(ctx, categoryID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get category")
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// Update fields
	if req.Name != nil {
		category.Name = *req.Name
		category.Slug = s.generateSlug(*req.Name)
	}
	if req.Description != nil {
		category.Description = *req.Description
	}
	if req.ImageURL != nil {
		category.ImageURL = *req.ImageURL
	}
	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}
	if req.IsActive != nil {
		category.IsActive = *req.IsActive
	}

	if err := s.categoryRepo.Update(ctx, category); err != nil {
		s.logger.WithError(err).Error("Failed to update category")
		return nil, fmt.Errorf("failed to update category: %w", err)
	}

	response := s.convertCategoryToResponse(*category)
	return &response, nil
}

func (s *MenuService) DeleteCategory(ctx context.Context, categoryID uuid.UUID) error {
	_, err := s.categoryRepo.GetByID(ctx, categoryID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get category")
		return fmt.Errorf("failed to get category: %w", err)
	}

	if err := s.categoryRepo.Delete(ctx, categoryID); err != nil {
		s.logger.WithError(err).Error("Failed to delete category")
		return fmt.Errorf("failed to delete category: %w", err)
	}

	return nil
}

// Menu Item methods
func (s *MenuService) GetMenuItems(ctx context.Context, branchID uuid.UUID, filters types.MenuItemFilters) (*types.MenuItemsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	items, total, err := s.itemRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu items")
		return nil, fmt.Errorf("failed to get menu items: %w", err)
	}

	// Convert to response format
	itemResponses := make([]types.MenuItemResponse, len(items))
	for i, item := range items {
		itemResponses[i] = s.convertMenuItemToResponse(item)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.MenuItemsResponse{
		Data:       itemResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *MenuService) GetMenuItemByID(ctx context.Context, branchID, itemID uuid.UUID) (*types.MenuItemResponse, error) {
	item, err := s.itemRepo.GetByID(ctx, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu item")
		return nil, fmt.Errorf("failed to get menu item: %w", err)
	}

	// Verify item belongs to branch
	if item.BranchID != branchID {
		return nil, fmt.Errorf("menu item not found")
	}

	response := s.convertMenuItemToResponse(*item)
	return &response, nil
}

func (s *MenuService) CreateMenuItem(ctx context.Context, branchID uuid.UUID, req types.CreateMenuItemRequest) (*types.MenuItemResponse, error) {
	// Verify category exists if provided
	if req.CategoryID != nil {
		category, err := s.categoryRepo.GetByID(ctx, *req.CategoryID)
		if err != nil || category.BranchID != branchID {
			return nil, fmt.Errorf("invalid category")
		}
	}

	item := &models.MenuItem{
		BranchID:        branchID,
		CategoryID:      req.CategoryID,
		Name:            req.Name,
		Description:     req.Description,
		Price:           req.Price,
		Cost:            req.Cost,
		Images:          models.ImagesData(req.Images),
		Ingredients:     models.IngredientsData(req.Ingredients),
		Allergens:       models.AllergensData(req.Allergens),
		PreparationTime: req.PreparationTime,
		IsAvailable:     req.IsAvailable,
		IsVegetarian:    req.IsVegetarian,
		IsVegan:         req.IsVegan,
		IsGlutenFree:    req.IsGlutenFree,
		IsSpicy:         req.IsSpicy,
		SpiceLevel:      req.SpiceLevel,
		Tags:            models.TagsData(req.Tags),
	}

	// Convert nutritional info
	if req.NutritionalInfo != nil {
		item.NutritionalInfo = s.convertNutritionalInfoRequestToModel(*req.NutritionalInfo)
	}

	// Convert options
	if req.Options != nil {
		item.Options = s.convertOptionsRequestToModel(req.Options)
	}

	// Generate slug from name
	item.Slug = s.generateSlug(req.Name)

	if err := s.itemRepo.Create(ctx, item); err != nil {
		s.logger.WithError(err).Error("Failed to create menu item")
		return nil, fmt.Errorf("failed to create menu item: %w", err)
	}

	// Reload item with relationships
	createdItem, err := s.itemRepo.GetByID(ctx, item.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get created menu item")
		return nil, fmt.Errorf("failed to get created menu item")
	}

	response := s.convertMenuItemToResponse(*createdItem)
	return &response, nil
}

func (s *MenuService) UpdateMenuItem(ctx context.Context, branchID, itemID uuid.UUID, req types.UpdateMenuItemRequest) (*types.MenuItemResponse, error) {
	item, err := s.itemRepo.GetByID(ctx, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu item")
		return nil, fmt.Errorf("failed to get menu item: %w", err)
	}

	// Verify item belongs to branch
	if item.BranchID != branchID {
		return nil, fmt.Errorf("menu item not found")
	}

	// Verify category exists if provided
	if req.CategoryID != nil {
		category, err := s.categoryRepo.GetByID(ctx, *req.CategoryID)
		if err != nil || category.BranchID != branchID {
			return nil, fmt.Errorf("invalid category")
		}
		item.CategoryID = req.CategoryID
	}

	// Update fields
	if req.Name != nil {
		item.Name = *req.Name
		item.Slug = s.generateSlug(*req.Name)
	}
	if req.Description != nil {
		item.Description = *req.Description
	}
	if req.Price != nil {
		item.Price = *req.Price
	}
	if req.Cost != nil {
		item.Cost = req.Cost
	}
	if req.Images != nil {
		item.Images = models.ImagesData(req.Images)
	}
	if req.Ingredients != nil {
		item.Ingredients = models.IngredientsData(req.Ingredients)
	}
	if req.Allergens != nil {
		item.Allergens = models.AllergensData(req.Allergens)
	}
	if req.NutritionalInfo != nil {
		item.NutritionalInfo = s.convertNutritionalInfoRequestToModel(*req.NutritionalInfo)
	}
	if req.PreparationTime != nil {
		item.PreparationTime = req.PreparationTime
	}
	if req.IsAvailable != nil {
		item.IsAvailable = *req.IsAvailable
	}
	if req.IsVegetarian != nil {
		item.IsVegetarian = *req.IsVegetarian
	}
	if req.IsVegan != nil {
		item.IsVegan = *req.IsVegan
	}
	if req.IsGlutenFree != nil {
		item.IsGlutenFree = *req.IsGlutenFree
	}
	if req.IsSpicy != nil {
		item.IsSpicy = *req.IsSpicy
	}
	if req.SpiceLevel != nil {
		item.SpiceLevel = *req.SpiceLevel
	}
	if req.Tags != nil {
		item.Tags = models.TagsData(req.Tags)
	}
	if req.Options != nil {
		item.Options = s.convertOptionsRequestToModel(req.Options)
	}

	if err := s.itemRepo.Update(ctx, item); err != nil {
		s.logger.WithError(err).Error("Failed to update menu item")
		return nil, fmt.Errorf("failed to update menu item: %w", err)
	}

	response := s.convertMenuItemToResponse(*item)
	return &response, nil
}

func (s *MenuService) DeleteMenuItem(ctx context.Context, branchID, itemID uuid.UUID) error {
	item, err := s.itemRepo.GetByID(ctx, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu item")
		return fmt.Errorf("failed to get menu item: %w", err)
	}

	// Verify item belongs to branch
	if item.BranchID != branchID {
		return fmt.Errorf("menu item not found")
	}

	if err := s.itemRepo.Delete(ctx, itemID); err != nil {
		s.logger.WithError(err).Error("Failed to delete menu item")
		return fmt.Errorf("failed to delete menu item: %w", err)
	}

	return nil
}

func (s *MenuService) ToggleAvailability(ctx context.Context, branchID, itemID uuid.UUID, req types.ToggleAvailabilityRequest) (*types.MenuItemResponse, error) {
	item, err := s.itemRepo.GetByID(ctx, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get menu item")
		return nil, fmt.Errorf("failed to get menu item: %w", err)
	}

	// Verify item belongs to branch
	if item.BranchID != branchID {
		return nil, fmt.Errorf("menu item not found")
	}

	if err := s.itemRepo.UpdateAvailability(ctx, itemID, req.IsAvailable); err != nil {
		s.logger.WithError(err).Error("Failed to update menu item availability")
		return nil, fmt.Errorf("failed to update menu item availability: %w", err)
	}

	// Reload item with updated availability
	updatedItem, err := s.itemRepo.GetByID(ctx, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get updated menu item")
		return nil, fmt.Errorf("failed to get updated menu item")
	}

	response := s.convertMenuItemToResponse(*updatedItem)
	return &response, nil
}

func (s *MenuService) GetPopularItems(ctx context.Context, branchID uuid.UUID, limit int) (*types.PopularItemsResponse, error) {
	if limit <= 0 {
		limit = 10 // default limit
	}

	items, err := s.itemRepo.GetPopularItems(ctx, branchID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get popular items")
		return nil, fmt.Errorf("failed to get popular items: %w", err)
	}

	// Convert to response format
	itemResponses := make([]types.MenuItemResponse, len(items))
	for i, item := range items {
		itemResponses[i] = s.convertMenuItemToResponse(item)
	}

	return &types.PopularItemsResponse{
		Data:  itemResponses,
		Total: len(itemResponses),
	}, nil
}

// Helper functions
func (s *MenuService) generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	return slug
}

func (s *MenuService) convertCategoryToResponse(category models.MenuCategory) types.CategoryResponse {
	return types.CategoryResponse{
		ID:          category.ID,
		BranchID:    category.BranchID,
		Name:        category.Name,
		Slug:        category.Slug,
		Description: category.Description,
		ImageURL:    category.ImageURL,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		ItemCount:   0, // TODO: Count items in category
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}

func (s *MenuService) convertMenuItemToResponse(item models.MenuItem) types.MenuItemResponse {
	var categoryResponse *types.CategoryResponse
	if item.Category != nil {
		category := s.convertCategoryToResponse(*item.Category)
		categoryResponse = &category
	}

	return types.MenuItemResponse{
		ID:              item.ID,
		BranchID:        item.BranchID,
		CategoryID:      item.CategoryID,
		Category:        categoryResponse,
		Name:            item.Name,
		Slug:            item.Slug,
		Description:     item.Description,
		Price:           item.Price,
		Cost:            item.Cost,
		Images:          []string(item.Images),
		PrimaryImage:    item.GetPrimaryImage(),
		Ingredients:     []string(item.Ingredients),
		Allergens:       []string(item.Allergens),
		NutritionalInfo: s.convertNutritionalInfoModelToResponse(item.NutritionalInfo),
		PreparationTime: item.PreparationTime,
		IsAvailable:     item.IsAvailable,
		IsVegetarian:    item.IsVegetarian,
		IsVegan:         item.IsVegan,
		IsGlutenFree:    item.IsGlutenFree,
		IsSpicy:         item.IsSpicy,
		SpiceLevel:      item.SpiceLevel,
		SpiceLevelText:  item.GetSpiceLevelText(),
		Tags:            []string(item.Tags),
		Options:         s.convertOptionsModelToResponse(item.Options),
		DietaryInfo:     item.GetDietaryInfo(),
		CreatedAt:       item.CreatedAt,
		UpdatedAt:       item.UpdatedAt,
	}
}

func (s *MenuService) convertNutritionalInfoRequestToModel(req types.NutritionalInfoRequest) models.NutritionalInfoData {
	var calories int
	var protein, carbs, fat, fiber, sodium float64

	if req.Calories != nil {
		calories = *req.Calories
	}
	if req.Protein != nil {
		protein = *req.Protein
	}
	if req.Carbohydrates != nil {
		carbs = *req.Carbohydrates
	}
	if req.Fat != nil {
		fat = *req.Fat
	}
	if req.Fiber != nil {
		fiber = *req.Fiber
	}
	if req.Sodium != nil {
		sodium = *req.Sodium
	}

	return models.NutritionalInfoData{
		Calories: calories,
		Protein:  protein,
		Carbs:    carbs,
		Fat:      fat,
		Fiber:    fiber,
		Sodium:   sodium,
	}
}

func (s *MenuService) convertNutritionalInfoModelToResponse(info models.NutritionalInfoData) *types.NutritionalInfoResponse {
	return &types.NutritionalInfoResponse{
		Calories:      &info.Calories,
		Protein:       &info.Protein,
		Carbohydrates: &info.Carbs,
		Fat:           &info.Fat,
		Fiber:         &info.Fiber,
		Sodium:        &info.Sodium,
		// Note: The model doesn't have these fields, so we'll leave them nil
		Sugar:       nil,
		Cholesterol: nil,
		VitaminA:    nil,
		VitaminC:    nil,
		Calcium:     nil,
		Iron:        nil,
	}
}

func (s *MenuService) convertOptionsRequestToModel(req []types.MenuItemOptionRequest) models.OptionsData {
	options := make([]models.MenuItemOption, len(req))
	for i, optionReq := range req {
		choices := make([]models.MenuItemChoice, len(optionReq.Choices))
		for j, choiceReq := range optionReq.Choices {
			choices[j] = models.MenuItemChoice{
				Name:      choiceReq.Name,
				Price:     choiceReq.PriceAdjust,
				IsDefault: choiceReq.IsDefault,
			}
		}

		options[i] = models.MenuItemOption{
			Name:     optionReq.Name,
			Type:     optionReq.Type,
			Required: optionReq.IsRequired,
			Choices:  choices,
		}
	}
	return models.OptionsData(options)
}

func (s *MenuService) convertOptionsModelToResponse(options models.OptionsData) []types.MenuItemOptionResponse {
	responses := make([]types.MenuItemOptionResponse, len(options))
	for i, option := range options {
		choices := make([]types.MenuItemChoiceResponse, len(option.Choices))
		for j, choice := range option.Choices {
			choices[j] = types.MenuItemChoiceResponse{
				Name:        choice.Name,
				PriceAdjust: choice.Price,
				IsDefault:   choice.IsDefault,
			}
		}

		responses[i] = types.MenuItemOptionResponse{
			Name:       option.Name,
			Type:       option.Type,
			IsRequired: option.Required,
			MinChoices: 0, // Not stored in model
			MaxChoices: 0, // Not stored in model
			Choices:    choices,
		}
	}
	return responses
}
