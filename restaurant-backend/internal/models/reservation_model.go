package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Reservation represents a table reservation
type Reservation struct {
	BaseModel
	BranchID           uuid.UUID  `json:"branch_id" gorm:"type:uuid;not null;index"`
	CustomerName       string     `json:"customer_name" gorm:"type:varchar(255);not null"`
	CustomerPhone      string     `json:"customer_phone" gorm:"type:varchar(50);not null"`
	CustomerEmail      string     `json:"customer_email" gorm:"type:varchar(255)"`
	PartySize          int        `json:"party_size" gorm:"not null"`
	ReservationDate    time.Time  `json:"reservation_date" gorm:"not null;index"`
	ReservationTime    time.Time  `json:"reservation_time" gorm:"not null"`
	Duration           int        `json:"duration" gorm:"default:120"` // in minutes
	TableID            *uuid.UUID `json:"table_id" gorm:"type:uuid;index"`
	Status             string     `json:"status" gorm:"type:varchar(20);default:'pending'"`
	SpecialRequests    string     `json:"special_requests" gorm:"type:text"`
	Notes              string     `json:"notes" gorm:"type:text"`
	Source             string     `json:"source" gorm:"type:varchar(20);default:'phone'"`
	CheckedInAt        *time.Time `json:"checked_in_at"`
	CompletedAt        *time.Time `json:"completed_at"`
	CancelledAt        *time.Time `json:"cancelled_at"`
	CancellationReason string     `json:"cancellation_reason" gorm:"type:text"`

	// Relationships
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Table  *Table     `json:"table,omitempty" gorm:"foreignKey:TableID"`
}

// TableName specifies the table name for Reservation
func (Reservation) TableName() string {
	return "reservations"
}

// BeforeCreate hook for Reservation
func (r *Reservation) BeforeCreate(tx *gorm.DB) error {
	return r.BaseModel.BeforeCreate(tx)
}

// IsActive returns true if the reservation is active (not cancelled or completed)
func (r *Reservation) IsActive() bool {
	return r.Status != ReservationStatusCancelled && r.Status != ReservationStatusCompleted
}

// CanBeCheckedIn returns true if the reservation can be checked in
func (r *Reservation) CanBeCheckedIn() bool {
	return r.Status == ReservationStatusConfirmed
}

// CanBeCancelled returns true if the reservation can be cancelled
func (r *Reservation) CanBeCancelled() bool {
	return r.Status != ReservationStatusCompleted && r.Status != ReservationStatusCancelled
}

// GetEndTime returns the expected end time of the reservation
func (r *Reservation) GetEndTime() time.Time {
	return r.ReservationTime.Add(time.Duration(r.Duration) * time.Minute)
}

// IsOverdue returns true if the reservation is overdue for check-in
func (r *Reservation) IsOverdue() bool {
	if r.Status != ReservationStatusConfirmed {
		return false
	}

	// Consider overdue if 15 minutes past reservation time
	overdueTime := r.ReservationTime.Add(15 * time.Minute)
	return time.Now().After(overdueTime)
}

// GetDuration returns the actual duration if completed, or expected duration
func (r *Reservation) GetDuration() time.Duration {
	if r.CompletedAt != nil && r.CheckedInAt != nil {
		return r.CompletedAt.Sub(*r.CheckedInAt)
	}
	return time.Duration(r.Duration) * time.Minute
}

// GetStatusColor returns a color code for the reservation status
func (r *Reservation) GetStatusColor() string {
	switch r.Status {
	case ReservationStatusPending:
		return "#FFA500" // Orange
	case ReservationStatusConfirmed:
		return "#0066CC" // Blue
	case ReservationStatusSeated:
		return "#00CC66" // Green
	case ReservationStatusCompleted:
		return "#666666" // Gray
	case ReservationStatusCancelled:
		return "#CC0000" // Red
	case ReservationStatusNoShow:
		return "#FF6600" // Dark Orange
	default:
		return "#666666" // Gray
	}
}
