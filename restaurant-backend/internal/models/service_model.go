package models

import (
	"time"

	"github.com/google/uuid"
)

// Service represents a service offered by a merchant (e.g., dining, catering, events)
type Service struct {
	BaseModel
	MerchantID      uuid.UUID              `json:"merchant_id" gorm:"type:uuid;not null;index"`
	BranchID        *uuid.UUID             `json:"branch_id" gorm:"type:uuid;index"`
	Name            string                 `json:"name" gorm:"type:varchar(255);not null"`
	Description     string                 `json:"description" gorm:"type:text"`
	Category        string                 `json:"category" gorm:"type:varchar(100);not null"`
	Price           float64                `json:"price" gorm:"type:decimal(10,2);not null"`
	Duration        int                    `json:"duration" gorm:"not null"` // in minutes
	MaxCapacity     int                    `json:"max_capacity" gorm:"default:1"`
	RequiresStaff   bool                   `json:"requires_staff" gorm:"default:false"`
	PreparationTime int                    `json:"preparation_time" gorm:"default:0"` // in minutes
	CleanupTime     int                    `json:"cleanup_time" gorm:"default:0"`     // in minutes
	ImageURL        string                 `json:"image_url" gorm:"type:varchar(500)"`
	IsActive        bool                   `json:"is_active" gorm:"default:true"`
	Settings        map[string]interface{} `json:"settings" gorm:"type:jsonb"`

	// Relationships
	Shop          Shop           `json:"shop,omitempty" gorm:"foreignKey:MerchantID"`
	Branch        *ShopBranch    `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Appointments  []Appointment  `json:"appointments,omitempty" gorm:"foreignKey:ServiceID"`
	StaffServices []StaffService `json:"staff_services,omitempty" gorm:"foreignKey:ServiceID"`
}

// Appointment represents a booking for a service
type Appointment struct {
	BaseModel
	ServiceID          uuid.UUID  `json:"service_id" gorm:"type:uuid;not null;index"`
	StaffID            *uuid.UUID `json:"staff_id" gorm:"type:uuid;index"`
	CustomerName       string     `json:"customer_name" gorm:"type:varchar(255);not null"`
	CustomerEmail      string     `json:"customer_email" gorm:"type:varchar(255)"`
	CustomerPhone      string     `json:"customer_phone" gorm:"type:varchar(50);not null"`
	AppointmentDate    time.Time  `json:"appointment_date" gorm:"not null;index"`
	StartTime          time.Time  `json:"start_time" gorm:"not null"`
	EndTime            time.Time  `json:"end_time" gorm:"not null"`
	Duration           int        `json:"duration" gorm:"not null"` // in minutes
	PartySize          int        `json:"party_size" gorm:"default:1"`
	Status             string     `json:"status" gorm:"type:varchar(20);default:'pending'"`
	Notes              string     `json:"notes" gorm:"type:text"`
	SpecialRequests    string     `json:"special_requests" gorm:"type:text"`
	Source             string     `json:"source" gorm:"type:varchar(20);default:'website'"`
	TotalPrice         float64    `json:"total_price" gorm:"type:decimal(10,2)"`
	DepositPaid        float64    `json:"deposit_paid" gorm:"type:decimal(10,2);default:0"`
	CancellationReason string     `json:"cancellation_reason" gorm:"type:text"`
	CancelledAt        *time.Time `json:"cancelled_at"`
	ConfirmedAt        *time.Time `json:"confirmed_at"`
	CompletedAt        *time.Time `json:"completed_at"`

	// Relationships
	Service Service `json:"service,omitempty" gorm:"foreignKey:ServiceID"`
	Staff   *Staff  `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
}

// Staff represents a staff member who can provide services
type Staff struct {
	BaseModel
	MerchantID     uuid.UUID      `json:"merchant_id" gorm:"type:uuid;not null;index"`
	BranchID       *uuid.UUID     `json:"branch_id" gorm:"type:uuid;index"`
	UserID         *uuid.UUID     `json:"user_id" gorm:"type:uuid;index"`
	FirstName      string         `json:"first_name" gorm:"type:varchar(100);not null"`
	LastName       string         `json:"last_name" gorm:"type:varchar(100);not null"`
	Email          string         `json:"email" gorm:"type:varchar(255)"`
	Phone          string         `json:"phone" gorm:"type:varchar(50)"`
	Position       string         `json:"position" gorm:"type:varchar(100)"`
	Department     string         `json:"department" gorm:"type:varchar(100)"`
	EmployeeID     string         `json:"employee_id" gorm:"type:varchar(50);uniqueIndex"`
	HireDate       time.Time      `json:"hire_date"`
	Salary         *float64       `json:"salary" gorm:"type:decimal(10,2)"`
	HourlyRate     *float64       `json:"hourly_rate" gorm:"type:decimal(8,2)"`
	Status         string         `json:"status" gorm:"type:varchar(20);default:'active'"`
	AvatarURL      string         `json:"avatar_url" gorm:"type:varchar(500)"`
	Bio            string         `json:"bio" gorm:"type:text"`
	Specialties    []string       `json:"specialties" gorm:"type:jsonb"`
	Languages      []string       `json:"languages" gorm:"type:jsonb"`
	Certifications []string       `json:"certifications" gorm:"type:jsonb"`
	WorkSchedule   []WorkSchedule `json:"work_schedule" gorm:"type:jsonb"`
	IsActive       bool           `json:"is_active" gorm:"default:true"`

	// Relationships
	Shop          Shop           `json:"shop,omitempty" gorm:"foreignKey:MerchantID"`
	Branch        *ShopBranch    `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	User          *User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Appointments  []Appointment  `json:"appointments,omitempty" gorm:"foreignKey:StaffID"`
	StaffServices []StaffService `json:"staff_services,omitempty" gorm:"foreignKey:StaffID"`
}

// StaffService represents the many-to-many relationship between staff and services
type StaffService struct {
	StaffID   uuid.UUID `json:"staff_id" gorm:"type:uuid;primaryKey"`
	ServiceID uuid.UUID `json:"service_id" gorm:"type:uuid;primaryKey"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Relationships
	Staff   Staff   `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
	Service Service `json:"service,omitempty" gorm:"foreignKey:ServiceID"`
}

// ServiceAvailability represents available time slots for a service
type ServiceAvailability struct {
	BaseModel
	ServiceID       uuid.UUID  `json:"service_id" gorm:"type:uuid;not null;index"`
	StaffID         *uuid.UUID `json:"staff_id" gorm:"type:uuid;index"`
	Date            time.Time  `json:"date" gorm:"not null;index"`
	StartTime       time.Time  `json:"start_time" gorm:"not null"`
	EndTime         time.Time  `json:"end_time" gorm:"not null"`
	IsAvailable     bool       `json:"is_available" gorm:"default:true"`
	MaxBookings     int        `json:"max_bookings" gorm:"default:1"`
	CurrentBookings int        `json:"current_bookings" gorm:"default:0"`

	// Relationships
	Service Service `json:"service,omitempty" gorm:"foreignKey:ServiceID"`
	Staff   *Staff  `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
}

// TableName overrides for GORM
func (Service) TableName() string {
	return "services"
}

func (Appointment) TableName() string {
	return "appointments"
}

func (Staff) TableName() string {
	return "staff"
}

func (StaffService) TableName() string {
	return "staff_services"
}

func (ServiceAvailability) TableName() string {
	return "service_availability"
}
