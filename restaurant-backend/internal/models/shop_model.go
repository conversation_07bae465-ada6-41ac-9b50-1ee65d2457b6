package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Shop represents a business entity (can be restaurant, cafe, etc.)
type Shop struct {
	BaseModel
	OwnerID     uuid.UUID `json:"owner_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug        string    `json:"slug" gorm:"type:varchar(255);uniqueIndex;not null"`
	Description string    `json:"description" gorm:"type:text"`
	ShopType    string    `json:"shop_type" gorm:"type:varchar(50);not null"` // restaurant, cafe, bakery, etc.
	Email       string    `json:"email" gorm:"type:varchar(255)"`
	Phone       string    `json:"phone" gorm:"type:varchar(50)"`
	Website     string    `json:"website" gorm:"type:varchar(255)"`
	Logo        string    `json:"logo" gorm:"type:varchar(500)"`
	CoverImage  string    `json:"cover_image" gorm:"type:varchar(500)"`
	Address     Address   `json:"address" gorm:"embedded;embeddedPrefix:address_"`

	// Business details
	CuisineType string  `json:"cuisine_type" gorm:"type:varchar(100)"`
	PriceRange  string  `json:"price_range" gorm:"type:varchar(20)"` // $, $$, $$$, $$$$
	Rating      float64 `json:"rating" gorm:"type:decimal(3,2);default:0"`
	ReviewCount int     `json:"review_count" gorm:"default:0"`

	// Social media
	SocialMedia SocialMediaLinks `json:"social_media" gorm:"type:jsonb"`

	// Business settings
	Settings      ShopSettings  `json:"settings" gorm:"type:jsonb"`
	BusinessHours BusinessHours `json:"business_hours" gorm:"type:jsonb"`

	// Status
	Status     string `json:"status" gorm:"type:varchar(20);default:'active'"` // active, inactive, suspended
	IsVerified bool   `json:"is_verified" gorm:"default:false"`
	IsActive   bool   `json:"is_active" gorm:"default:true"`

	// Relationships
	Owner    User         `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
	Branches []ShopBranch `json:"branches,omitempty" gorm:"foreignKey:ShopID"`
}

// ShopBranch represents a branch/location of a shop
type ShopBranch struct {
	BaseModel
	ShopID  uuid.UUID `json:"shop_id" gorm:"type:uuid;not null;index"`
	Name    string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug    string    `json:"slug" gorm:"type:varchar(255);not null"`
	Email   string    `json:"email" gorm:"type:varchar(255)"`
	Phone   string    `json:"phone" gorm:"type:varchar(50)"`
	Address Address   `json:"address" gorm:"embedded;embeddedPrefix:address_"`

	// Branch-specific settings
	Settings      BranchSettings `json:"settings" gorm:"type:jsonb"`
	BusinessHours BusinessHours  `json:"business_hours" gorm:"type:jsonb"`
	Timezone      string         `json:"timezone" gorm:"type:varchar(50);default:'UTC'"`

	// Status
	Status   string `json:"status" gorm:"type:varchar(20);default:'active'"`
	IsActive bool   `json:"is_active" gorm:"default:true"`

	// Relationships
	Shop Shop `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
}

// SocialMediaLinks represents social media links
type SocialMediaLinks struct {
	Facebook  string `json:"facebook"`
	Instagram string `json:"instagram"`
	Twitter   string `json:"twitter"`
	LinkedIn  string `json:"linkedin"`
	YouTube   string `json:"youtube"`
	TikTok    string `json:"tiktok"`
}

// BusinessHours represents business hours for each day of the week
type BusinessHours map[string]string

// Scan implements the sql.Scanner interface for SocialMediaLinks
func (s *SocialMediaLinks) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for SocialMediaLinks
func (s SocialMediaLinks) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface for BusinessHours
func (b *BusinessHours) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, b)
}

// Value implements the driver.Valuer interface for BusinessHours
func (b BusinessHours) Value() (driver.Value, error) {
	return json.Marshal(b)
}

// ShopSettings represents shop-level settings
type ShopSettings struct {
	Currency             string            `json:"currency"`
	TaxRate              float64           `json:"tax_rate"`
	ServiceChargeRate    float64           `json:"service_charge_rate"`
	DefaultTipPercentage float64           `json:"default_tip_percentage"`
	PaymentMethods       []string          `json:"payment_methods"`
	Features             map[string]bool   `json:"features"`
	Notifications        map[string]bool   `json:"notifications"`
	Theme                map[string]string `json:"theme"`
	OnlineOrdering       bool              `json:"online_ordering"`
	TableReservations    bool              `json:"table_reservations"`
	DeliveryEnabled      bool              `json:"delivery_enabled"`
	PickupEnabled        bool              `json:"pickup_enabled"`
}

// Scan implements the sql.Scanner interface for ShopSettings
func (s *ShopSettings) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for ShopSettings
func (s ShopSettings) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// BranchSettings represents branch-level settings
type BranchSettings struct {
	Currency             string            `json:"currency"`
	TaxRate              float64           `json:"tax_rate"`
	ServiceChargeRate    float64           `json:"service_charge_rate"`
	DefaultTipPercentage float64           `json:"default_tip_percentage"`
	PaymentMethods       []string          `json:"payment_methods"`
	Features             map[string]bool   `json:"features"`
	Notifications        map[string]bool   `json:"notifications"`
	Theme                map[string]string `json:"theme"`
	OnlineOrdering       bool              `json:"online_ordering"`
	TableReservations    bool              `json:"table_reservations"`
	DeliveryEnabled      bool              `json:"delivery_enabled"`
	PickupEnabled        bool              `json:"pickup_enabled"`
	MaxTableCapacity     int               `json:"max_table_capacity"`
	ReservationWindow    int               `json:"reservation_window"`   // days in advance
	MinReservationTime   int               `json:"min_reservation_time"` // hours in advance
}

// Scan implements the sql.Scanner interface for BranchSettings
func (b *BranchSettings) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, b)
}

// Value implements the driver.Valuer interface for BranchSettings
func (b BranchSettings) Value() (driver.Value, error) {
	return json.Marshal(b)
}

// BeforeCreate hook for Shop
func (s *Shop) BeforeCreate(tx *gorm.DB) error {
	// Call the base model's BeforeCreate
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}

	// Set default settings if not provided
	if s.Settings.Currency == "" {
		s.Settings = ShopSettings{
			Currency:             "USD",
			TaxRate:              0.08,
			ServiceChargeRate:    0.0,
			DefaultTipPercentage: 15.0,
			PaymentMethods:       []string{"cash", "credit_card", "debit_card"},
			Features: map[string]bool{
				"online_ordering":    true,
				"table_reservations": true,
				"qr_menu":            true,
				"reviews":            true,
				"analytics":          true,
			},
			Notifications: map[string]bool{
				"new_orders":    true,
				"reservations":  true,
				"reviews":       true,
				"low_inventory": true,
			},
			Theme: map[string]string{
				"primary_color":   "#3B82F6",
				"secondary_color": "#10B981",
				"accent_color":    "#F59E0B",
			},
			OnlineOrdering:    true,
			TableReservations: true,
			DeliveryEnabled:   false,
			PickupEnabled:     true,
		}
	}

	// Set default business hours if not provided
	if len(s.BusinessHours) == 0 {
		s.BusinessHours = BusinessHours{
			"monday":    "09:00-22:00",
			"tuesday":   "09:00-22:00",
			"wednesday": "09:00-22:00",
			"thursday":  "09:00-22:00",
			"friday":    "09:00-23:00",
			"saturday":  "09:00-23:00",
			"sunday":    "10:00-21:00",
		}
	}

	return nil
}

// BeforeCreate hook for ShopBranch
func (sb *ShopBranch) BeforeCreate(tx *gorm.DB) error {
	if sb.ID == uuid.Nil {
		sb.ID = uuid.New()
	}

	// Set default settings if not provided
	if sb.Settings.Currency == "" {
		sb.Settings = BranchSettings{
			Currency:             "USD",
			TaxRate:              0.08,
			ServiceChargeRate:    0.0,
			DefaultTipPercentage: 15.0,
			PaymentMethods:       []string{"cash", "credit_card", "debit_card"},
			Features: map[string]bool{
				"online_ordering":    true,
				"table_reservations": true,
				"qr_menu":            true,
				"reviews":            true,
				"analytics":          true,
			},
			Notifications: map[string]bool{
				"new_orders":    true,
				"reservations":  true,
				"reviews":       true,
				"low_inventory": true,
			},
			Theme: map[string]string{
				"primary_color":   "#3B82F6",
				"secondary_color": "#10B981",
				"accent_color":    "#F59E0B",
			},
			OnlineOrdering:     true,
			TableReservations:  true,
			DeliveryEnabled:    false,
			PickupEnabled:      true,
			MaxTableCapacity:   12,
			ReservationWindow:  30,
			MinReservationTime: 2,
		}
	}

	// Set default business hours if not provided
	if len(sb.BusinessHours) == 0 {
		sb.BusinessHours = BusinessHours{
			"monday":    "09:00-22:00",
			"tuesday":   "09:00-22:00",
			"wednesday": "09:00-22:00",
			"thursday":  "09:00-22:00",
			"friday":    "09:00-23:00",
			"saturday":  "09:00-23:00",
			"sunday":    "10:00-21:00",
		}
	}

	return nil
}

// TableName overrides for GORM
func (Shop) TableName() string {
	return "shops"
}

func (ShopBranch) TableName() string {
	return "shop_branches"
}
