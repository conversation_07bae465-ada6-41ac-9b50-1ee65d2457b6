package repositories

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// InventoryRepository defines the interface for inventory data access
type InventoryRepository interface {
	// Suppliers
	CreateSupplier(ctx context.Context, supplier *models.Supplier) error
	GetSupplier(ctx context.Context, id uuid.UUID) (*models.Supplier, error)
	GetSuppliers(ctx context.Context) ([]*models.Supplier, error)
	UpdateSupplier(ctx context.Context, supplier *models.Supplier) error
	DeleteSupplier(ctx context.Context, id uuid.UUID) error

	// Ingredients
	CreateIngredient(ctx context.Context, ingredient *models.Ingredient) error
	GetIngredient(ctx context.Context, id uuid.UUID) (*models.Ingredient, error)
	GetIngredients(ctx context.Context) ([]*models.Ingredient, error)
	GetIngredientsByCategory(ctx context.Context, category string) ([]*models.Ingredient, error)
	UpdateIngredient(ctx context.Context, ingredient *models.Ingredient) error
	DeleteIngredient(ctx context.Context, id uuid.UUID) error

	// Inventory Items
	CreateInventoryItem(ctx context.Context, item *models.InventoryItem) error
	GetInventoryItem(ctx context.Context, branchID, ingredientID uuid.UUID) (*models.InventoryItem, error)
	GetInventoryItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error)
	UpdateInventoryItem(ctx context.Context, item *models.InventoryItem) error
	DeleteInventoryItem(ctx context.Context, id uuid.UUID) error
	GetLowStockItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error)
	GetExpiringItems(ctx context.Context, branchID uuid.UUID, days int) ([]*models.InventoryItem, error)

	// Menu Ingredients
	CreateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error
	GetMenuIngredients(ctx context.Context, menuItemID uuid.UUID) ([]*models.MenuIngredient, error)
	UpdateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error
	DeleteMenuIngredient(ctx context.Context, id uuid.UUID) error

	// Purchase Orders
	CreatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error
	GetPurchaseOrder(ctx context.Context, id uuid.UUID) (*models.PurchaseOrder, error)
	GetPurchaseOrders(ctx context.Context, branchID uuid.UUID) ([]*models.PurchaseOrder, error)
	UpdatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error
	DeletePurchaseOrder(ctx context.Context, id uuid.UUID) error

	// Stock Movements
	CreateStockMovement(ctx context.Context, movement *models.StockMovement) error
	GetStockMovements(ctx context.Context, branchID uuid.UUID, ingredientID *uuid.UUID) ([]*models.StockMovement, error)
	GetRecentStockMovements(ctx context.Context, branchID uuid.UUID, limit int) ([]*models.StockMovement, error)

	// Waste Records
	CreateWasteRecord(ctx context.Context, waste *models.WasteRecord) error
	GetWasteRecords(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.WasteRecord, error)

	// Analytics
	GetTopIngredientsByUsage(ctx context.Context, branchID uuid.UUID, limit int) ([]types.IngredientUsage, error)
	GetInventoryValue(ctx context.Context, branchID uuid.UUID) (float64, error)
}

type inventoryRepository struct {
	db *gorm.DB
}

// NewInventoryRepository creates a new inventory repository
func NewInventoryRepository(db *gorm.DB) InventoryRepository {
	return &inventoryRepository{db: db}
}

// Suppliers implementation
func (r *inventoryRepository) CreateSupplier(ctx context.Context, supplier *models.Supplier) error {
	return r.db.WithContext(ctx).Create(supplier).Error
}

func (r *inventoryRepository) GetSupplier(ctx context.Context, id uuid.UUID) (*models.Supplier, error) {
	var supplier models.Supplier
	err := r.db.WithContext(ctx).Preload("Ingredients").First(&supplier, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &supplier, nil
}

func (r *inventoryRepository) GetSuppliers(ctx context.Context) ([]*models.Supplier, error) {
	var suppliers []*models.Supplier
	err := r.db.WithContext(ctx).Where("status = ?", models.SupplierStatusActive).Find(&suppliers).Error
	return suppliers, err
}

func (r *inventoryRepository) UpdateSupplier(ctx context.Context, supplier *models.Supplier) error {
	return r.db.WithContext(ctx).Save(supplier).Error
}

func (r *inventoryRepository) DeleteSupplier(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Supplier{}, id).Error
}

// Ingredients implementation
func (r *inventoryRepository) CreateIngredient(ctx context.Context, ingredient *models.Ingredient) error {
	return r.db.WithContext(ctx).Create(ingredient).Error
}

func (r *inventoryRepository) GetIngredient(ctx context.Context, id uuid.UUID) (*models.Ingredient, error) {
	var ingredient models.Ingredient
	err := r.db.WithContext(ctx).Preload("Supplier").First(&ingredient, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &ingredient, nil
}

func (r *inventoryRepository) GetIngredients(ctx context.Context) ([]*models.Ingredient, error) {
	var ingredients []*models.Ingredient
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Where("is_active = ?", true).
		Order("name ASC").
		Find(&ingredients).Error
	return ingredients, err
}

func (r *inventoryRepository) GetIngredientsByCategory(ctx context.Context, category string) ([]*models.Ingredient, error) {
	var ingredients []*models.Ingredient
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Where("category = ? AND is_active = ?", category, true).
		Order("name ASC").
		Find(&ingredients).Error
	return ingredients, err
}

func (r *inventoryRepository) UpdateIngredient(ctx context.Context, ingredient *models.Ingredient) error {
	return r.db.WithContext(ctx).Save(ingredient).Error
}

func (r *inventoryRepository) DeleteIngredient(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&models.Ingredient{}).Where("id = ?", id).Update("is_active", false).Error
}

// Inventory Items implementation
func (r *inventoryRepository) CreateInventoryItem(ctx context.Context, item *models.InventoryItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

func (r *inventoryRepository) GetInventoryItem(ctx context.Context, branchID, ingredientID uuid.UUID) (*models.InventoryItem, error) {
	var item models.InventoryItem
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("Ingredient.Supplier").
		Where("branch_id = ? AND ingredient_id = ?", branchID, ingredientID).
		First(&item).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

func (r *inventoryRepository) GetInventoryItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error) {
	var items []*models.InventoryItem
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("Ingredient.Supplier").
		Where("branch_id = ?", branchID).
		Order("ingredient.name ASC").
		Find(&items).Error
	return items, err
}

func (r *inventoryRepository) UpdateInventoryItem(ctx context.Context, item *models.InventoryItem) error {
	return r.db.WithContext(ctx).Save(item).Error
}

func (r *inventoryRepository) DeleteInventoryItem(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.InventoryItem{}, id).Error
}

func (r *inventoryRepository) GetLowStockItems(ctx context.Context, branchID uuid.UUID) ([]*models.InventoryItem, error) {
	var items []*models.InventoryItem
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Joins("JOIN ingredients ON inventory_items.ingredient_id = ingredients.id").
		Where("inventory_items.branch_id = ? AND inventory_items.available_stock <= ingredients.reorder_point", branchID).
		Find(&items).Error
	return items, err
}

func (r *inventoryRepository) GetExpiringItems(ctx context.Context, branchID uuid.UUID, days int) ([]*models.InventoryItem, error) {
	var items []*models.InventoryItem
	expiryDate := time.Now().AddDate(0, 0, days)
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Where("branch_id = ? AND expiry_date IS NOT NULL AND expiry_date <= ?", branchID, expiryDate).
		Order("expiry_date ASC").
		Find(&items).Error
	return items, err
}

// Menu Ingredients implementation
func (r *inventoryRepository) CreateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error {
	return r.db.WithContext(ctx).Create(menuIngredient).Error
}

func (r *inventoryRepository) GetMenuIngredients(ctx context.Context, menuItemID uuid.UUID) ([]*models.MenuIngredient, error) {
	var menuIngredients []*models.MenuIngredient
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Where("menu_item_id = ?", menuItemID).
		Find(&menuIngredients).Error
	return menuIngredients, err
}

func (r *inventoryRepository) UpdateMenuIngredient(ctx context.Context, menuIngredient *models.MenuIngredient) error {
	return r.db.WithContext(ctx).Save(menuIngredient).Error
}

func (r *inventoryRepository) DeleteMenuIngredient(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.MenuIngredient{}, id).Error
}

// Purchase Orders implementation
func (r *inventoryRepository) CreatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error {
	return r.db.WithContext(ctx).Create(po).Error
}

func (r *inventoryRepository) GetPurchaseOrder(ctx context.Context, id uuid.UUID) (*models.PurchaseOrder, error) {
	var po models.PurchaseOrder
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Preload("Items").
		Preload("Items.Ingredient").
		Preload("Creator").
		Preload("Approver").
		Preload("Receiver").
		First(&po, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &po, nil
}

func (r *inventoryRepository) GetPurchaseOrders(ctx context.Context, branchID uuid.UUID) ([]*models.PurchaseOrder, error) {
	var pos []*models.PurchaseOrder
	err := r.db.WithContext(ctx).
		Preload("Supplier").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Find(&pos).Error
	return pos, err
}

func (r *inventoryRepository) UpdatePurchaseOrder(ctx context.Context, po *models.PurchaseOrder) error {
	return r.db.WithContext(ctx).Save(po).Error
}

func (r *inventoryRepository) DeletePurchaseOrder(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.PurchaseOrder{}, id).Error
}

// Stock Movements implementation
func (r *inventoryRepository) CreateStockMovement(ctx context.Context, movement *models.StockMovement) error {
	return r.db.WithContext(ctx).Create(movement).Error
}

func (r *inventoryRepository) GetStockMovements(ctx context.Context, branchID uuid.UUID, ingredientID *uuid.UUID) ([]*models.StockMovement, error) {
	query := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ?", branchID)

	if ingredientID != nil {
		query = query.Where("ingredient_id = ?", *ingredientID)
	}

	var movements []*models.StockMovement
	err := query.Order("created_at DESC").Find(&movements).Error
	return movements, err
}

func (r *inventoryRepository) GetRecentStockMovements(ctx context.Context, branchID uuid.UUID, limit int) ([]*models.StockMovement, error) {
	var movements []*models.StockMovement
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Limit(limit).
		Find(&movements).Error
	return movements, err
}

// Waste Records implementation
func (r *inventoryRepository) CreateWasteRecord(ctx context.Context, waste *models.WasteRecord) error {
	return r.db.WithContext(ctx).Create(waste).Error
}

func (r *inventoryRepository) GetWasteRecords(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.WasteRecord, error) {
	var records []*models.WasteRecord
	err := r.db.WithContext(ctx).
		Preload("Ingredient").
		Preload("User").
		Where("branch_id = ? AND waste_date >= ? AND waste_date <= ?", branchID, startDate, endDate).
		Order("waste_date DESC").
		Find(&records).Error
	return records, err
}

// Analytics implementation
func (r *inventoryRepository) GetTopIngredientsByUsage(ctx context.Context, branchID uuid.UUID, limit int) ([]types.IngredientUsage, error) {
	var results []types.IngredientUsage

	// This would typically involve complex queries to calculate usage
	// For now, return empty slice
	return results, nil
}

func (r *inventoryRepository) GetInventoryValue(ctx context.Context, branchID uuid.UUID) (float64, error) {
	var totalValue float64
	err := r.db.WithContext(ctx).
		Model(&models.InventoryItem{}).
		Where("branch_id = ?", branchID).
		Select("COALESCE(SUM(total_value), 0)").
		Scan(&totalValue).Error
	return totalValue, err
}
