package repositories

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ServiceRepository struct {
	db *gorm.DB
}

func NewServiceRepository(db *gorm.DB) *ServiceRepository {
	return &ServiceRepository{db: db}
}

// Service methods
func (r *ServiceRepository) GetServices(ctx context.Context, merchantID uuid.UUID, filters types.ServiceFilters) ([]models.Service, int64, error) {
	var services []models.Service
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Service{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Category != "" {
		query = query.Where("category = ?", filters.Category)
	}
	if filters.RequiresStaff != nil {
		query = query.Where("requires_staff = ?", *filters.RequiresStaff)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.MinPrice != nil {
		query = query.Where("price >= ?", *filters.MinPrice)
	}
	if filters.MaxPrice != nil {
		query = query.Where("price <= ?", *filters.MaxPrice)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).Find(&services).Error; err != nil {
		return nil, 0, err
	}

	return services, total, nil
}

func (r *ServiceRepository) GetServiceByID(ctx context.Context, serviceID uuid.UUID) (*models.Service, error) {
	var service models.Service
	if err := r.db.WithContext(ctx).First(&service, "id = ?", serviceID).Error; err != nil {
		return nil, err
	}
	return &service, nil
}

func (r *ServiceRepository) CreateService(ctx context.Context, service *models.Service) error {
	return r.db.WithContext(ctx).Create(service).Error
}

func (r *ServiceRepository) UpdateService(ctx context.Context, serviceID uuid.UUID, updates map[string]interface{}) (*models.Service, error) {
	var service models.Service
	if err := r.db.WithContext(ctx).First(&service, "id = ?", serviceID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&service).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &service, nil
}

func (r *ServiceRepository) DeleteService(ctx context.Context, serviceID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Service{}, "id = ?", serviceID).Error
}

// Appointment methods
func (r *ServiceRepository) GetAppointments(ctx context.Context, merchantID uuid.UUID, filters types.AppointmentFilters) ([]models.Appointment, int64, error) {
	var appointments []models.Appointment
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Appointment{}).
		Joins("JOIN services ON appointments.service_id = services.id").
		Where("services.merchant_id = ?", merchantID)

	// Apply filters
	if filters.ServiceID != uuid.Nil {
		query = query.Where("appointments.service_id = ?", filters.ServiceID)
	}
	if filters.StaffID != uuid.Nil {
		query = query.Where("appointments.staff_id = ?", filters.StaffID)
	}
	if filters.Status != "" {
		query = query.Where("appointments.status = ?", filters.Status)
	}
	if filters.Date != "" {
		date, err := time.Parse("2006-01-02", filters.Date)
		if err == nil {
			query = query.Where("DATE(appointments.appointment_date) = ?", date.Format("2006-01-02"))
		}
	}
	if filters.StartDate != "" && filters.EndDate != "" {
		startDate, err1 := time.Parse("2006-01-02", filters.StartDate)
		endDate, err2 := time.Parse("2006-01-02", filters.EndDate)
		if err1 == nil && err2 == nil {
			query = query.Where("appointments.appointment_date BETWEEN ? AND ?", startDate, endDate)
		}
	}
	if filters.Search != "" {
		query = query.Where("appointments.customer_name ILIKE ? OR appointments.customer_email ILIKE ? OR appointments.customer_phone ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and preload relationships
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Preload("Service").Preload("Staff").
		Offset(offset).Limit(filters.Limit).
		Order("appointments.appointment_date DESC").
		Find(&appointments).Error; err != nil {
		return nil, 0, err
	}

	return appointments, total, nil
}

func (r *ServiceRepository) GetAppointmentByID(ctx context.Context, appointmentID uuid.UUID) (*models.Appointment, error) {
	var appointment models.Appointment
	if err := r.db.WithContext(ctx).Preload("Service").Preload("Staff").
		First(&appointment, "id = ?", appointmentID).Error; err != nil {
		return nil, err
	}
	return &appointment, nil
}

func (r *ServiceRepository) CreateAppointment(ctx context.Context, appointment *models.Appointment) error {
	return r.db.WithContext(ctx).Create(appointment).Error
}

func (r *ServiceRepository) UpdateAppointment(ctx context.Context, appointmentID uuid.UUID, updates map[string]interface{}) (*models.Appointment, error) {
	var appointment models.Appointment
	if err := r.db.WithContext(ctx).First(&appointment, "id = ?", appointmentID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&appointment).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Reload with relationships
	if err := r.db.WithContext(ctx).Preload("Service").Preload("Staff").
		First(&appointment, "id = ?", appointmentID).Error; err != nil {
		return nil, err
	}

	return &appointment, nil
}

func (r *ServiceRepository) CancelAppointment(ctx context.Context, appointmentID uuid.UUID, reason string) (*models.Appointment, error) {
	now := time.Now()
	updates := map[string]interface{}{
		"status":              "cancelled",
		"cancellation_reason": reason,
		"cancelled_at":        &now,
	}

	return r.UpdateAppointment(ctx, appointmentID, updates)
}

// Staff methods
func (r *ServiceRepository) GetStaff(ctx context.Context, merchantID uuid.UUID, filters types.StaffFilters) ([]models.Staff, int64, error) {
	var staff []models.Staff
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Staff{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Position != "" {
		query = query.Where("position = ?", filters.Position)
	}
	if filters.Department != "" {
		query = query.Where("department = ?", filters.Department)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		query = query.Where("first_name ILIKE ? OR last_name ILIKE ? OR email ILIKE ? OR employee_id ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "first_name, last_name"
	if filters.SortBy != "" {
		sortOrder := "ASC"
		if filters.SortOrder == "desc" {
			sortOrder = "DESC"
		}

		switch filters.SortBy {
		case "first_name", "last_name", "position", "department", "status", "hire_date", "created_at":
			orderBy = filters.SortBy + " " + sortOrder
		default:
			orderBy = "first_name, last_name"
		}
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).
		Order(orderBy).
		Find(&staff).Error; err != nil {
		return nil, 0, err
	}

	return staff, total, nil
}

func (r *ServiceRepository) GetStaffByID(ctx context.Context, staffID uuid.UUID) (*models.Staff, error) {
	var staff models.Staff
	if err := r.db.WithContext(ctx).First(&staff, "id = ?", staffID).Error; err != nil {
		return nil, err
	}
	return &staff, nil
}

func (r *ServiceRepository) CreateStaff(ctx context.Context, staff *models.Staff) error {
	return r.db.WithContext(ctx).Create(staff).Error
}

func (r *ServiceRepository) UpdateStaff(ctx context.Context, staffID uuid.UUID, updates map[string]interface{}) (*models.Staff, error) {
	var staff models.Staff
	if err := r.db.WithContext(ctx).First(&staff, "id = ?", staffID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&staff).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &staff, nil
}

func (r *ServiceRepository) DeleteStaff(ctx context.Context, staffID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Staff{}, "id = ?", staffID).Error
}

// Availability methods
func (r *ServiceRepository) GetServiceAvailability(ctx context.Context, serviceID uuid.UUID, date time.Time, staffID *uuid.UUID) ([]models.ServiceAvailability, error) {
	var availability []models.ServiceAvailability

	query := r.db.WithContext(ctx).Where("service_id = ? AND DATE(date) = ?", serviceID, date.Format("2006-01-02"))

	if staffID != nil {
		query = query.Where("staff_id = ?", *staffID)
	}

	if err := query.Find(&availability).Error; err != nil {
		return nil, err
	}

	return availability, nil
}

func (r *ServiceRepository) GetAvailableTimeSlots(ctx context.Context, serviceID uuid.UUID, date time.Time, staffID *uuid.UUID) ([]string, error) {
	// This is a simplified implementation
	// In a real application, you would calculate available slots based on:
	// - Service duration
	// - Staff working hours
	// - Existing appointments
	// - Service availability settings

	timeSlots := []string{
		"09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
		"12:00", "12:30", "13:00", "13:30", "14:00", "14:30",
		"15:00", "15:30", "16:00", "16:30", "17:00", "17:30",
		"18:00", "18:30", "19:00", "19:30", "20:00", "20:30",
	}

	// Get existing appointments for the date
	var appointments []models.Appointment
	query := r.db.WithContext(ctx).Where("service_id = ? AND DATE(appointment_date) = ? AND status NOT IN (?)",
		serviceID, date.Format("2006-01-02"), []string{"cancelled", "completed"})

	if staffID != nil {
		query = query.Where("staff_id = ?", *staffID)
	}

	if err := query.Find(&appointments).Error; err != nil {
		return nil, err
	}

	// Remove booked slots (simplified logic)
	bookedSlots := make(map[string]bool)
	for _, appointment := range appointments {
		timeSlot := appointment.StartTime.Format("15:04")
		bookedSlots[timeSlot] = true
	}

	var availableSlots []string
	for _, slot := range timeSlots {
		if !bookedSlots[slot] {
			availableSlots = append(availableSlots, slot)
		}
	}

	return availableSlots, nil
}
