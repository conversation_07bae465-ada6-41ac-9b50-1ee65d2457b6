package repositories

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ReservationRepository defines the interface for reservation data access
type ReservationRepository interface {
	Create(ctx context.Context, reservation *models.Reservation) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Reservation, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.ReservationFilters) ([]models.Reservation, int64, error)
	Update(ctx context.Context, reservation *models.Reservation) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetTodayReservations(ctx context.Context, branchID uuid.UUID) ([]models.Reservation, error)
	GetAvailability(ctx context.Context, branchID uuid.UUID, date time.Time) ([]types.TimeSlot, error)
}

type reservationRepository struct {
	db *gorm.DB
}

func NewReservationRepository(db *gorm.DB) ReservationRepository {
	return &reservationRepository{db: db}
}

func (r *reservationRepository) Create(ctx context.Context, reservation *models.Reservation) error {
	return r.db.WithContext(ctx).Create(reservation).Error
}

func (r *reservationRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Reservation, error) {
	var reservation models.Reservation
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		First(&reservation, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &reservation, nil
}

func (r *reservationRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.ReservationFilters) ([]models.Reservation, int64, error) {
	query := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Date != "" {
		query = query.Where("DATE(reservation_date) = ?", filters.Date)
	}
	if filters.StartDate != "" && filters.EndDate != "" {
		query = query.Where("DATE(reservation_date) BETWEEN ? AND ?", filters.StartDate, filters.EndDate)
	}
	if filters.TableID != nil {
		query = query.Where("table_id = ?", *filters.TableID)
	}
	if filters.Search != "" {
		query = query.Where("customer_name ILIKE ? OR customer_phone ILIKE ? OR customer_email ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	var total int64
	if err := query.Model(&models.Reservation{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Order by reservation date and time
	query = query.Order("reservation_date ASC, reservation_time ASC")

	var reservations []models.Reservation
	if err := query.Find(&reservations).Error; err != nil {
		return nil, 0, err
	}

	return reservations, total, nil
}

func (r *reservationRepository) Update(ctx context.Context, reservation *models.Reservation) error {
	return r.db.WithContext(ctx).Save(reservation).Error
}

func (r *reservationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Reservation{}, id).Error
}

func (r *reservationRepository) GetTodayReservations(ctx context.Context, branchID uuid.UUID) ([]models.Reservation, error) {
	today := time.Now().Format("2006-01-02")
	
	var reservations []models.Reservation
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Where("branch_id = ? AND DATE(reservation_date) = ?", branchID, today).
		Order("reservation_time ASC").
		Find(&reservations).Error
	
	return reservations, err
}

func (r *reservationRepository) GetAvailability(ctx context.Context, branchID uuid.UUID, date time.Time) ([]types.TimeSlot, error) {
	// This is a simplified implementation
	// In a real system, you'd calculate availability based on table capacity, existing reservations, etc.
	timeSlots := []types.TimeSlot{
		{Time: "11:00", Available: true},
		{Time: "11:30", Available: true},
		{Time: "12:00", Available: false},
		{Time: "12:30", Available: true},
		{Time: "13:00", Available: true},
		{Time: "13:30", Available: false},
		{Time: "14:00", Available: true},
		{Time: "18:00", Available: true},
		{Time: "18:30", Available: true},
		{Time: "19:00", Available: false},
		{Time: "19:30", Available: true},
		{Time: "20:00", Available: true},
		{Time: "20:30", Available: true},
		{Time: "21:00", Available: true},
	}
	
	return timeSlots, nil
}
