package handlers

import (
	"net/http"
	"strconv"

	"restaurant-backend/internal/api/middleware"
	"restaurant-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// InventoryHandler handles inventory-related HTTP requests
type InventoryHandler struct {
	inventoryService *services.InventoryService
	logger           *logrus.Logger
}

// NewInventoryHandler creates a new inventory handler
func NewInventoryHandler(inventoryService *services.InventoryService, logger *logrus.Logger) *InventoryHandler {
	return &InventoryHandler{
		inventoryService: inventoryService,
		logger:           logger,
	}
}

// GetDashboard godoc
// @Summary Get inventory dashboard
// @Description Get comprehensive inventory dashboard data for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} services.InventoryDashboard
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/dashboard [get]
func (h *InventoryHandler) GetDashboard(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	dashboard, err := h.inventoryService.GetInventoryDashboard(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get inventory dashboard")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get inventory dashboard"})
		return
	}

	c.JSON(http.StatusOK, dashboard)
}

// CreateIngredient godoc
// @Summary Create a new ingredient
// @Description Create a new ingredient in the system
// @Tags inventory
// @Accept json
// @Produce json
// @Param ingredient body services.CreateIngredientRequest true "Ingredient data"
// @Success 201 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/ingredients [post]
func (h *InventoryHandler) CreateIngredient(c *gin.Context) {
	var req services.CreateIngredientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ingredient, err := h.inventoryService.CreateIngredient(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create ingredient")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create ingredient"})
		return
	}

	c.JSON(http.StatusCreated, ingredient)
}

// UpdateStock godoc
// @Summary Update stock levels
// @Description Update stock levels for an ingredient at a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param stock body services.UpdateStockRequest true "Stock update data"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/stock [put]
func (h *InventoryHandler) UpdateStock(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := middleware.GetUserIDAsUUID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.UpdateStockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	inventoryItem, err := h.inventoryService.UpdateStock(c.Request.Context(), branchID, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update stock")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, inventoryItem)
}

// CreatePurchaseOrder godoc
// @Summary Create a purchase order
// @Description Create a new purchase order for ingredients
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param order body services.CreatePurchaseOrderRequest true "Purchase order data"
// @Success 201 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/purchase-orders [post]
func (h *InventoryHandler) CreatePurchaseOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := middleware.GetUserIDAsUUID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.CreatePurchaseOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	purchaseOrder, err := h.inventoryService.CreatePurchaseOrder(c.Request.Context(), branchID, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create purchase order")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create purchase order"})
		return
	}

	c.JSON(http.StatusCreated, purchaseOrder)
}

// GetLowStockItems godoc
// @Summary Get low stock items
// @Description Get items that are below their reorder point
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {array} services.LowStockAlert
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/low-stock [get]
func (h *InventoryHandler) GetLowStockItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	lowStockItems, err := h.inventoryService.GetLowStockItems(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get low stock items")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get low stock items"})
		return
	}

	c.JSON(http.StatusOK, lowStockItems)
}

// GetExpiringItems godoc
// @Summary Get expiring items
// @Description Get items that are expiring within a specified number of days
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param days query int false "Number of days to look ahead (default: 7)"
// @Success 200 {array} services.ExpiryAlert
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/expiring [get]
func (h *InventoryHandler) GetExpiringItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse days parameter
	days := 7 // Default to 7 days
	if daysStr := c.Query("days"); daysStr != "" {
		if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 {
			days = parsedDays
		}
	}

	expiringItems, err := h.inventoryService.GetExpiringItems(c.Request.Context(), branchID, days)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get expiring items")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get expiring items"})
		return
	}

	c.JSON(http.StatusOK, expiringItems)
}

// GetInventoryItems godoc
// @Summary Get inventory items
// @Description Get all inventory items for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category query string false "Filter by ingredient category"
// @Param status query string false "Filter by status (available, reserved, expired, damaged)"
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/items [get]
func (h *InventoryHandler) GetInventoryItems(c *gin.Context) {
	_, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	items := []gin.H{}

	c.JSON(http.StatusOK, items)
}

// GetIngredients godoc
// @Summary Get ingredients
// @Description Get all available ingredients
// @Tags inventory
// @Accept json
// @Produce json
// @Param category query string false "Filter by category"
// @Param supplier_id query string false "Filter by supplier ID"
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/ingredients [get]
func (h *InventoryHandler) GetIngredients(c *gin.Context) {
	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	ingredients := []gin.H{}

	c.JSON(http.StatusOK, ingredients)
}

// GetSuppliers godoc
// @Summary Get suppliers
// @Description Get all active suppliers
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/suppliers [get]
func (h *InventoryHandler) GetSuppliers(c *gin.Context) {
	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	suppliers := []gin.H{}

	c.JSON(http.StatusOK, suppliers)
}

// GetPurchaseOrders godoc
// @Summary Get purchase orders
// @Description Get purchase orders for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param status query string false "Filter by status (pending, approved, ordered, received, cancelled)"
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/purchase-orders [get]
func (h *InventoryHandler) GetPurchaseOrders(c *gin.Context) {
	_, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	purchaseOrders := []gin.H{}

	c.JSON(http.StatusOK, purchaseOrders)
}

// GetStockMovements godoc
// @Summary Get stock movements
// @Description Get stock movement history for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param ingredient_id query string false "Filter by ingredient ID"
// @Param movement_type query string false "Filter by movement type (in, out, adjustment, waste)"
// @Param limit query int false "Number of records to return (default: 50)"
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/movements [get]
func (h *InventoryHandler) GetStockMovements(c *gin.Context) {
	_, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse query parameters
	_ = 50
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			_ = parsedLimit
		}
	}

	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	movements := []gin.H{}

	c.JSON(http.StatusOK, movements)
}

// GetWasteRecords godoc
// @Summary Get waste records
// @Description Get food waste records for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/waste [get]
func (h *InventoryHandler) GetWasteRecords(c *gin.Context) {
	_, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	wasteRecords := []gin.H{}

	c.JSON(http.StatusOK, wasteRecords)
}
