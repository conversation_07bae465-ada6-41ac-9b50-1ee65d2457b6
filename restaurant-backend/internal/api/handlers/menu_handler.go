package handlers

import (
	"net/http"
	"strconv"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// MenuHandler handles menu-related HTTP requests
type MenuHandler struct {
	menuService *services.MenuService
	logger      *logrus.Logger
}

func NewMenuHandler(menuService *services.MenuService, logger *logrus.Logger) *MenuHandler {
	return &MenuHandler{menuService: menuService, logger: logger}
}

// Category handlers

// GetCategories godoc
// @Summary Get all menu categories for a branch
// @Description Get all menu categories for a specific branch with filtering and pagination
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.CategoriesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories [get]
func (h *MenuHandler) GetCategories(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.CategoryFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	categories, err := h.menuService.GetCategories(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get categories: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get categories"})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// CreateCategory godoc
// @Summary Create a new menu category
// @Description Create a new menu category for a branch
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category body types.CreateCategoryRequest true "Category data"
// @Success 201 {object} types.CategoryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories [post]
func (h *MenuHandler) CreateCategory(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	category, err := h.menuService.CreateCategory(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateCategory godoc
// @Summary Update a menu category
// @Description Update a specific menu category by ID
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param categoryId path string true "Category ID"
// @Param category body types.UpdateCategoryRequest true "Category update data"
// @Success 200 {object} types.CategoryResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories/{categoryId} [put]
func (h *MenuHandler) UpdateCategory(c *gin.Context) {
	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req types.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	category, err := h.menuService.UpdateCategory(c.Request.Context(), categoryID, req)
	if err != nil {
		h.logger.Error("Failed to update category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteCategory godoc
// @Summary Delete a menu category
// @Description Delete a specific menu category by ID
// @Tags menu-categories
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param categoryId path string true "Category ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/categories/{categoryId} [delete]
func (h *MenuHandler) DeleteCategory(c *gin.Context) {
	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	if err := h.menuService.DeleteCategory(c.Request.Context(), categoryID); err != nil {
		h.logger.Error("Failed to delete category: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.Status(http.StatusNoContent)
}

// Menu Item handlers

// GetMenuItems godoc
// @Summary Get all menu items for a branch
// @Description Get all menu items for a specific branch with filtering and pagination
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category_id query string false "Filter by category ID"
// @Param is_available query bool false "Filter by availability"
// @Param is_vegetarian query bool false "Filter by vegetarian"
// @Param is_vegan query bool false "Filter by vegan"
// @Param is_gluten_free query bool false "Filter by gluten free"
// @Param is_spicy query bool false "Filter by spicy"
// @Param min_price query number false "Minimum price filter"
// @Param max_price query number false "Maximum price filter"
// @Param search query string false "Search term"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {object} types.MenuItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items [get]
func (h *MenuHandler) GetMenuItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.MenuItemFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	items, err := h.menuService.GetMenuItems(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get menu items: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get menu items"})
		return
	}

	c.JSON(http.StatusOK, items)
}

// GetMenuItem godoc
// @Summary Get a specific menu item
// @Description Get a specific menu item by ID
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId} [get]
func (h *MenuHandler) GetMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	item, err := h.menuService.GetMenuItemByID(c.Request.Context(), branchID, itemID)
	if err != nil {
		h.logger.Error("Failed to get menu item: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// CreateMenuItem godoc
// @Summary Create a new menu item
// @Description Create a new menu item for a branch
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param item body types.CreateMenuItemRequest true "Menu item data"
// @Success 201 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items [post]
func (h *MenuHandler) CreateMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateMenuItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	item, err := h.menuService.CreateMenuItem(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create menu item: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create menu item"})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// UpdateMenuItem godoc
// @Summary Update a menu item
// @Description Update a specific menu item by ID
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Param item body types.UpdateMenuItemRequest true "Menu item update data"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId} [put]
func (h *MenuHandler) UpdateMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	var req types.UpdateMenuItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	item, err := h.menuService.UpdateMenuItem(c.Request.Context(), branchID, itemID, req)
	if err != nil {
		h.logger.Error("Failed to update menu item: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update menu item"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// DeleteMenuItem godoc
// @Summary Delete a menu item
// @Description Delete a specific menu item by ID
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId} [delete]
func (h *MenuHandler) DeleteMenuItem(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	if err := h.menuService.DeleteMenuItem(c.Request.Context(), branchID, itemID); err != nil {
		h.logger.Error("Failed to delete menu item: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete menu item"})
		return
	}

	c.Status(http.StatusNoContent)
}

// ToggleAvailability godoc
// @Summary Toggle menu item availability
// @Description Toggle the availability status of a specific menu item
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param itemId path string true "Menu Item ID"
// @Param availability body types.ToggleAvailabilityRequest true "Availability data"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/menu/items/{itemId}/availability [patch]
func (h *MenuHandler) ToggleAvailability(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid item ID"})
		return
	}

	var req types.ToggleAvailabilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	item, err := h.menuService.ToggleAvailability(c.Request.Context(), branchID, itemID, req)
	if err != nil {
		h.logger.Error("Failed to toggle menu item availability: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to toggle menu item availability"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// GetPopularItems godoc
// @Summary Get popular menu items
// @Description Get popular menu items for a branch
// @Tags menu-items
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param limit query int false "Number of items to return"
// @Success 200 {object} types.PopularItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/reports/popular-items [get]
func (h *MenuHandler) GetPopularItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	limit := 10 // default
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	items, err := h.menuService.GetPopularItems(c.Request.Context(), branchID, limit)
	if err != nil {
		h.logger.Error("Failed to get popular items: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular items"})
		return
	}

	c.JSON(http.StatusOK, items)
}
