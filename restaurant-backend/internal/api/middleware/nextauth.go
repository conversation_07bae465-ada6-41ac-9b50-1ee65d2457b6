package middleware

import (
	"encoding/base64"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// NextAuthPayload represents the decoded NextAuth token payload
type NextAuthPayload struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	Role      string `json:"role"`
	IssuedAt  int64  `json:"issued_at"`
	ExpiresAt int64  `json:"expires_at"`
}

// NextAuthRequired middleware validates NextAuth tokens from frontend
func NextAuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for NextAuth headers first
		userID := c.GetHeader("X-User-ID")
		userEmail := c.GetHeader("X-User-Email")
		userRole := c.GetHeader("X-User-Role")
		authSource := c.GetHeader("X-Auth-Source")

		// If we have NextAuth headers, validate them
		if authSource == "nextauth" && userID != "" && userEmail != "" {
			// Validate user ID format (support both UUID and OAuth provider IDs)
			if !isValidUserID(userID) {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
				c.Abort()
				return
			}

			// Validate the Authorization header token if present
			authHeader := c.GetHeader("Authorization")
			if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				if !validateNextAuthToken(token, userID, userEmail, userRole) {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid NextAuth token"})
					c.Abort()
					return
				}
			}

			// Set user context - store user ID as string to support different formats
			c.Set("user_id", userID)
			c.Set("user_email", userEmail)
			c.Set("user_role", userRole)
			c.Set("auth_source", "nextauth")

			c.Next()
			return
		}

		// Fall back to regular JWT token validation
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token required"})
			c.Abort()
			return
		}

		// Try to validate as NextAuth token first
		if payload := decodeNextAuthToken(tokenString); payload != nil {
			if validateNextAuthPayload(payload) {
				// Validate user ID format (support both UUID and OAuth provider IDs)
				if !isValidUserID(payload.UserID) {
					c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID in token"})
					c.Abort()
					return
				}

				// Set user context - store user ID as string to support different formats
				c.Set("user_id", payload.UserID)
				c.Set("user_email", payload.Email)
				c.Set("user_role", payload.Role)
				c.Set("auth_source", "nextauth")
				c.Next()
				return
			}
		}

		// If not NextAuth token, return unauthorized
		// In a real implementation, you might want to fall back to JWT validation
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
		c.Abort()
	}
}

// OptionalNextAuth middleware validates NextAuth tokens if present but doesn't require them
func OptionalNextAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check for NextAuth headers
		userID := c.GetHeader("X-User-ID")
		userEmail := c.GetHeader("X-User-Email")
		userRole := c.GetHeader("X-User-Role")
		authSource := c.GetHeader("X-Auth-Source")

		if authSource == "nextauth" && userID != "" && userEmail != "" {
			if isValidUserID(userID) {
				// Set user context - store user ID as string to support different formats
				c.Set("user_id", userID)
				c.Set("user_email", userEmail)
				c.Set("user_role", userRole)
				c.Set("auth_source", "nextauth")
			}
		}

		c.Next()
	}
}

// isValidUserID validates user ID format (supports both UUID and OAuth provider IDs)
func isValidUserID(userID string) bool {
	if userID == "" {
		return false
	}

	// Try to parse as UUID first
	if _, err := uuid.Parse(userID); err == nil {
		return true
	}

	// For OAuth providers (Google, GitHub, etc.), user IDs are often numeric strings
	// Validate that it's a reasonable length and contains only alphanumeric characters
	if len(userID) >= 10 && len(userID) <= 50 {
		for _, char := range userID {
			if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || char == '-' || char == '_') {
				return false
			}
		}
		return true
	}

	return false
}

// validateNextAuthToken validates the NextAuth token against the provided user info
func validateNextAuthToken(token, userID, userEmail, userRole string) bool {
	payload := decodeNextAuthToken(token)
	if payload == nil {
		return false
	}

	// Validate that token matches the headers
	return payload.UserID == userID &&
		payload.Email == userEmail &&
		payload.Role == userRole &&
		validateNextAuthPayload(payload)
}

// decodeNextAuthToken decodes a base64-encoded NextAuth token
func decodeNextAuthToken(token string) *NextAuthPayload {
	// Decode base64
	decoded, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return nil
	}

	// Parse JSON
	var payload NextAuthPayload
	if err := json.Unmarshal(decoded, &payload); err != nil {
		return nil
	}

	return &payload
}

// validateNextAuthPayload validates the NextAuth token payload
func validateNextAuthPayload(payload *NextAuthPayload) bool {
	// Check required fields
	if payload.UserID == "" || payload.Email == "" {
		return false
	}

	// Check expiration
	now := time.Now().Unix()
	if payload.ExpiresAt > 0 && payload.ExpiresAt < now {
		return false
	}

	return true
}

// RequireNextAuthRole middleware checks if user has required role for NextAuth
func RequireNextAuthRole(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "No role information found"})
			c.Abort()
			return
		}

		role, ok := userRole.(string)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{"error": "Invalid role format"})
			c.Abort()
			return
		}

		// Check if user has one of the allowed roles
		hasRole := false
		for _, allowedRole := range allowedRoles {
			if role == allowedRole {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}
