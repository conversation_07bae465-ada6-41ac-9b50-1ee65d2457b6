package main

import (
	"encoding/json"
	"log"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/internal/models"
)

func main() {
	log.Println("🔧 Floor Data Fix Tool")
	log.Println("======================")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Connect to database
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Fix existing floors with invalid layout data
	log.Println("Fixing existing floors...")

	// Get all floors
	var floors []models.Floor
	if err := db.Find(&floors).Error; err != nil {
		log.Fatalf("Failed to get floors: %v", err)
	}

	log.Printf("Found %d floors to check", len(floors))

	for _, floor := range floors {
		log.Printf("Checking floor: %s (ID: %s)", floor.Name, floor.ID)

		// Create a default layout
		defaultLayout := models.FloorLayout{
			Width:    800,
			Height:   600,
			GridSize: 20,
			ShowGrid: true,
		}

		// Update the floor with proper layout
		if err := db.Model(&floor).Update("layout", defaultLayout).Error; err != nil {
			log.Printf("Failed to update floor %s: %v", floor.Name, err)
			continue
		}

		log.Printf("✅ Updated floor: %s", floor.Name)
	}

	// Test creating a new floor to verify scanning works
	log.Println("Testing floor creation...")

	testLayout := models.FloorLayout{
		Width:           1000,
		Height:          800,
		BackgroundImage: "",
		GridSize:        25,
		ShowGrid:        true,
	}

	// Convert to JSON to test
	jsonData, err := json.Marshal(testLayout)
	if err != nil {
		log.Fatalf("Failed to marshal test layout: %v", err)
	}

	log.Printf("Test layout JSON: %s", string(jsonData))

	// Test scanning
	var scannedLayout models.FloorLayout
	if err := scannedLayout.Scan(jsonData); err != nil {
		log.Fatalf("Failed to scan test layout: %v", err)
	}

	log.Printf("✅ Scanning test successful: %+v", scannedLayout)

	log.Println("🎉 Floor data fix completed successfully!")
}
