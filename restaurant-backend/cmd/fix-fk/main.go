package main

import (
	"fmt"
	"log"
	"os"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/internal/models"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔧 Foreign Key Constraint Fix Tool")
	fmt.Println("==================================")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database connection
	dsn := cfg.GetDSN()
	logger := logrus.New()
	logger.SetOutput(os.Stdout)
	logger.SetLevel(logrus.InfoLevel)
	db, err := database.Initialize(dsn, logger)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Check current state
	fmt.Println("\n📊 Analyzing current database state...")
	if err := analyzeCurrentState(db); err != nil {
		log.Fatalf("Failed to analyze database: %v", err)
	}

	// Ask for confirmation
	fmt.Print("\n❓ Do you want to proceed with the fix? (y/N): ")
	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" {
		fmt.Println("❌ Operation cancelled.")
		os.Exit(0)
	}

	// Run the fix
	fmt.Println("\n🔧 Running foreign key constraint fix...")
	if err := fixForeignKeyConstraints(db); err != nil {
		log.Fatalf("Failed to fix foreign key constraints: %v", err)
	}

	fmt.Println("\n🎉 Foreign key constraint fix completed successfully!")
	fmt.Println("✅ You can now run the backend server.")
}

func analyzeCurrentState(db *gorm.DB) error {
	// Check if users table exists
	if !db.Migrator().HasTable(&models.User{}) {
		fmt.Println("ℹ️  Users table doesn't exist yet - no fix needed")
		return nil
	}

	// Check if shop_branches table exists
	if !db.Migrator().HasTable(&models.ShopBranch{}) {
		fmt.Println("ℹ️  ShopBranches table doesn't exist yet - no fix needed")
		return nil
	}

	// Count total users
	var totalUsers int64
	db.Model(&models.User{}).Count(&totalUsers)

	// Count users with non-null branch_id
	var usersWithBranchID int64
	db.Model(&models.User{}).Where("branch_id IS NOT NULL").Count(&usersWithBranchID)

	// Count orphaned users (users with branch_id that doesn't exist in shop_branches)
	var orphanedUsers int64
	err := db.Raw(`
		SELECT COUNT(*)
		FROM users u
		WHERE u.branch_id IS NOT NULL
		AND u.branch_id NOT IN (SELECT id FROM shop_branches)
	`).Scan(&orphanedUsers).Error

	if err != nil {
		return err
	}

	// Count total shop branches
	var totalBranches int64
	db.Model(&models.ShopBranch{}).Count(&totalBranches)

	fmt.Printf("📈 Database Analysis:\n")
	fmt.Printf("   • Total users: %d\n", totalUsers)
	fmt.Printf("   • Users with branch_id: %d\n", usersWithBranchID)
	fmt.Printf("   • Users with invalid branch_id: %d\n", orphanedUsers)
	fmt.Printf("   • Total shop branches: %d\n", totalBranches)

	if orphanedUsers == 0 {
		fmt.Println("✅ No foreign key constraint issues found!")
		return nil
	}

	fmt.Printf("⚠️  Found %d users with invalid branch_id that need fixing\n", orphanedUsers)
	return nil
}

func fixForeignKeyConstraints(db *gorm.DB) error {
	// First, set branch_id to NULL for users with invalid branch_id references
	result := db.Exec(`
		UPDATE users
		SET branch_id = NULL, updated_at = NOW()
		WHERE branch_id IS NOT NULL
		AND branch_id NOT IN (SELECT id FROM shop_branches)
	`)

	if result.Error != nil {
		return fmt.Errorf("failed to fix orphaned user branch references: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		fmt.Printf("✅ Fixed %d users with invalid branch_id references\n", result.RowsAffected)
	}

	// Now try to create the foreign key constraint
	fmt.Println("🔗 Creating foreign key constraint...")
	err := db.Exec(`
		ALTER TABLE users 
		ADD CONSTRAINT fk_users_branch 
		FOREIGN KEY (branch_id) REFERENCES shop_branches(id)
	`).Error

	if err != nil {
		// Check if constraint already exists
		if err.Error() == `ERROR: relation "fk_users_branch" already exists (SQLSTATE 42P07)` {
			fmt.Println("ℹ️  Foreign key constraint already exists")
			return nil
		}
		return fmt.Errorf("failed to create foreign key constraint: %v", err)
	}

	fmt.Println("✅ Foreign key constraint created successfully")
	return nil
}
