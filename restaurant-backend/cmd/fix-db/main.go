package main

import (
	"fmt"
	"log"
	"os"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/internal/models"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔧 Database Integrity Fix Tool")
	fmt.Println("==============================")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database connection
	dsn := cfg.GetDSN()
	logger := logrus.New()
	logger.SetOutput(os.Stdout)
	logger.SetLevel(logrus.InfoLevel)
	db, err := database.Initialize(dsn, logger)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Check current state
	fmt.Println("\n📊 Analyzing current database state...")
	if err := analyzeCurrentState(db); err != nil {
		log.Fatalf("Failed to analyze database: %v", err)
	}

	// Ask for confirmation
	fmt.Print("\n❓ Do you want to proceed with the fix? (y/N): ")
	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" {
		fmt.Println("❌ Operation cancelled.")
		os.Exit(0)
	}

	// Run the fix
	fmt.Println("\n🔧 Running database integrity fix...")
	if err := fixDatabaseIntegrity(db); err != nil {
		log.Fatalf("Failed to fix database: %v", err)
	}

	// Verify the fix
	fmt.Println("\n✅ Verifying the fix...")
	if err := verifyFix(db); err != nil {
		log.Fatalf("Verification failed: %v", err)
	}

	// Try running migrations
	fmt.Println("\n🚀 Attempting to run migrations...")
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	fmt.Println("\n🎉 Database integrity fix completed successfully!")
	fmt.Println("✅ All foreign key constraints should now work properly.")
}

func analyzeCurrentState(db *gorm.DB) error {
	// Check if tables exist
	if !db.Migrator().HasTable(&models.Shop{}) {
		fmt.Println("ℹ️  Shops table doesn't exist yet - no fix needed")
		return nil
	}

	if !db.Migrator().HasTable(&models.User{}) {
		fmt.Println("ℹ️  Users table doesn't exist yet - no fix needed")
		return nil
	}

	// Count orphaned shops
	var orphanedCount int64
	err := db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		LEFT JOIN users u ON s.owner_id = u.id
		WHERE u.id IS NULL AND s.owner_id IS NOT NULL
	`).Scan(&orphanedCount).Error

	if err != nil {
		return err
	}

	// Count shops with NULL owner_id
	var nullOwnerCount int64
	err = db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		WHERE s.owner_id IS NULL
	`).Scan(&nullOwnerCount).Error

	if err != nil {
		return err
	}

	// Count total shops
	var totalShops int64
	db.Model(&models.Shop{}).Count(&totalShops)

	// Count total users
	var totalUsers int64
	db.Model(&models.User{}).Count(&totalUsers)

	fmt.Printf("📈 Database Analysis:\n")
	fmt.Printf("   • Total shops: %d\n", totalShops)
	fmt.Printf("   • Total users: %d\n", totalUsers)
	fmt.Printf("   • Shops with invalid owner_id: %d\n", orphanedCount)
	fmt.Printf("   • Shops with NULL owner_id: %d\n", nullOwnerCount)
	fmt.Printf("   • Total problematic shops: %d\n", orphanedCount+nullOwnerCount)

	if orphanedCount == 0 && nullOwnerCount == 0 {
		fmt.Println("✅ No integrity issues found!")
		return nil
	}

	fmt.Printf("⚠️  Found %d shops that need fixing\n", orphanedCount+nullOwnerCount)
	return nil
}

func fixDatabaseIntegrity(db *gorm.DB) error {
	// Create system user
	systemUserID := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	systemUser := models.User{
		BaseModel: models.BaseModel{
			ID: systemUserID,
		},
		Email:     "<EMAIL>",
		FirstName: "System",
		LastName:  "User",
		Status:    models.UserStatusActive,
	}

	// Set password
	if err := systemUser.SetPassword("system123!"); err != nil {
		return fmt.Errorf("failed to set system user password: %v", err)
	}

	// Create system user if it doesn't exist
	var existingUser models.User
	result := db.Where("id = ?", systemUserID).First(&existingUser)
	if result.Error != nil && result.Error == gorm.ErrRecordNotFound {
		if err := db.Create(&systemUser).Error; err != nil {
			return fmt.Errorf("failed to create system user: %v", err)
		}
		fmt.Println("✅ Created system user")
	} else {
		fmt.Println("ℹ️  System user already exists")
	}

	// Fix orphaned shops
	result = db.Exec(`
		UPDATE shops
		SET owner_id = ?, updated_at = NOW()
		WHERE owner_id IS NOT NULL
		AND owner_id NOT IN (SELECT id FROM users)
	`, systemUserID)

	if result.Error != nil {
		return fmt.Errorf("failed to fix orphaned shops: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		fmt.Printf("✅ Fixed %d orphaned shops\n", result.RowsAffected)
	}

	// Fix shops with NULL owner_id
	result = db.Exec(`
		UPDATE shops
		SET owner_id = ?, updated_at = NOW()
		WHERE owner_id IS NULL
	`, systemUserID)

	if result.Error != nil {
		return fmt.Errorf("failed to fix NULL owner shops: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		fmt.Printf("✅ Fixed %d shops with NULL owner_id\n", result.RowsAffected)
	}

	return nil
}

func verifyFix(db *gorm.DB) error {
	// Check for remaining orphaned shops
	var orphanedCount int64
	err := db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		LEFT JOIN users u ON s.owner_id = u.id
		WHERE u.id IS NULL AND s.owner_id IS NOT NULL
	`).Scan(&orphanedCount).Error

	if err != nil {
		return err
	}

	// Check for shops with NULL owner_id
	var nullOwnerCount int64
	err = db.Raw(`
		SELECT COUNT(*)
		FROM shops s
		WHERE s.owner_id IS NULL
	`).Scan(&nullOwnerCount).Error

	if err != nil {
		return err
	}

	if orphanedCount > 0 {
		return fmt.Errorf("still have %d orphaned shops", orphanedCount)
	}

	if nullOwnerCount > 0 {
		return fmt.Errorf("still have %d shops with NULL owner_id", nullOwnerCount)
	}

	fmt.Println("✅ All shops now have valid owner references")
	return nil
}
