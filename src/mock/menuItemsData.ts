export interface MenuItem {
  id: string;
  slug: string;
  name: string;
  category: 'Appetizers' | 'Main Courses' | 'Desserts' | 'Drinks';
  description: string;
  price: number;
  image: string;
  available: boolean;
}

export const menuItems: MenuItem[] = [
  {
    id: '1',
    slug: 'margherita-pizza',
    name: 'Margherita Pizza',
    category: 'Main Courses',
    description: 'Classic pizza with fresh basil, mozzarella, and tomato sauce.',
    price: 12.99,
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA9DvQOEOCBGSqFNHliGV-FQ2c16-xhFlN66v96JNNQrfBzTAT45pahmf1Oj507BBlgdbalFOx4IRMgkLAg0F9zakm_MA5MjsHbJxFQbFt6R86iMrf97xYBjkW9owBibzB6o1eMoYa5EcOWJ7U3AvAZlYe5yuzLG4EXinqmVN_gP_SuqjIFB6ufOddblpVjfJk3HnZOCrN-TB1DWjTQeRWsMAqmuxWB_Va7zZqSdCsHEjs-A1n6oqIuEmT8QVC_cPuIf-fqpes6Olqy',
    available: true
  },
  {
    id: '2',
    slug: 'caesar-salad',
    name: 'Caesar Salad',
    category: 'Appetizers',
    description: 'Romaine lettuce, croutons, Parmesan cheese, and Caesar dressing.',
    price: 8.99,
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBl6JnbqGWrUWv5vuIXFXHpeC8cyw-dhNh93A2RNzlrkaCo66hASZF3E2Pxw0Pb667CVP1q97gOSc_x9m7a2wOkAzrGL5g5BvZWoFYzzMSEZbzKq0qF6RS46G6j-vgzpkmkJ_S6L0vDw4DALmP_3BoaLJJYBmHJWFiCQ-0XDnAGJqlDaqMOOGRRRLVGKfw072NXLrBLWgceQab1MI3P4NHouEB2Z0B0V2c5r__CWRXWbQ22Lp97U8nJG0GSu8r1TDIBcnuI1EMapDk5',
    available: true
  },
  {
    id: '3',
    slug: 'spaghetti-carbonara',
    name: 'Spaghetti Carbonara',
    category: 'Main Courses',
    description: 'Spaghetti with a creamy sauce, pancetta, and Parmesan cheese.',
    price: 14.99,
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuC_v127WSbOGYSzZReKAk_rpz8J9Isc6EzHcOqmpTs3eUJSzaMaqhsde62Vp4OUp5E3W9tZS0owY19vI9UYMtl6DetIPWHj-hX3zrKbBGM8u09b8StMLAmkHNTV5O13Oyuj-h_LpWXj5FuEXumzTI9Z3NGKfXesXAaCdBTwoSBz6rpJPMcTpP4JNGfdM18zWYcOg7MX3x6OKH0-r-castrXDwaMJ0adDxtR7bLWaCO1wKg1DRaGI3R7BlqhTDgwjFkTj-87uYX0j7VY',
    available: true
  },
  {
    id: '4',
    slug: 'grilled-salmon',
    name: 'Grilled Salmon',
    category: 'Main Courses',
    description: 'Grilled salmon fillet with lemon butter sauce and seasonal vegetables.',
    price: 18.99,
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCNaFPf1TwmiX1nuwNdOgysEIhZ-EVizReHKKOZ8140niqj7FpoM3TM0eid3KuK7gB9nS4V2oozMAUgDm7b1lxFo1-Rwf3eWr_lS9Hv7WO3FMgVnXIgtD_Q97dXvXgfjz4EFOVXBvkDvxjfM7N9kSeE_Ky1peHjS8NeXMA0O7OZQJhyA55-xhth4wuOX6JY2xFG3l87JHBzImg-LDBCpQSHZ8_wknmwaY-41r0QjA_u-kr9S3Ppt0LQG8zEw7N2NK9xSdJl0YWKlC02',
    available: false
  },
  {
    id: '5',
    slug: 'chocolate-brownie',
    name: 'Chocolate Brownie',
    category: 'Desserts',
    description: 'Rich chocolate brownie served warm with vanilla ice cream.',
    price: 6.99,
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDKMcYwIKlS-pBqF06CWaqko8CXjM3uALCbJ8G89f56zAgWiRVFct6FCR_VEvBGw0n7s-0Fp1einTit-a7xr-7ZZ_E0vdsIXJiuKN62MfGuju4OwIi9zsihzS9Qx45biHXg1grpa_pLyS4I-3NT-ouXljoePPSi-BCBIJdbli3OJv-cBnr2wbtcja2PqNlZjh1AZQ_PcFb9p2tAxO-FAoVyCg20I8jYdfmpnTfxMsulsL_poRTa9vwoYP0HWC9Nhts8RRUPSWa7VaPd',
    available: true
  },
  {
    id: '6',
    slug: 'chicken-caesar-salad',
    name: 'Chicken Caesar Salad',
    category: 'Main Courses',
    description: 'Classic salad with grilled chicken, romaine lettuce, croutons, and parmesan cheese, tossed in Caesar dressing.',
    price: 12.99,
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDRh-Iujf6T_5dPpRakN22DFlZ5W6yo-N3g_0rZ0dXKANYECoueAVgvIxeRKrWps0PTWnVG-eawBa8_KTEnS2Mkzf9KUBDA2MVBysv3xEfNeuYFlSUxxckRHM3SP8JThz467bnc6b3NnTu3s3KEK4SQp1yTqC7OMUjU7EkrV2WckgexZT7LBqN36jWvoQR5OBYiokN4KYPgPTnUdSbiXHPpYisJe4zKl8SrwMeunsYSbmwrP6ogIdQzAwDyzDuc0n0dnC0VVijgKsGH',
    available: true
  },
  {
    id: '7',
    slug: 'bruschetta',
    name: 'Bruschetta',
    category: 'Appetizers',
    description: 'Toasted bread topped with diced tomatoes, fresh basil, and garlic.',
    price: 8.99,
    image: 'https://via.placeholder.com/400x400/f4f2f1/161412?text=Bruschetta',
    available: true
  },
  {
    id: '8',
    slug: 'chicken-alfredo',
    name: 'Chicken Alfredo',
    category: 'Main Courses',
    description: 'Fettuccine pasta with creamy Alfredo sauce and grilled chicken breast.',
    price: 16.99,
    image: 'https://via.placeholder.com/400x400/f4f2f1/161412?text=Chicken+Alfredo',
    available: true
  },
  {
    id: '9',
    slug: 'mozzarella-sticks',
    name: 'Mozzarella Sticks',
    category: 'Appetizers',
    description: 'Breaded and fried mozzarella cheese sticks served with marinara sauce.',
    price: 7.99,
    image: 'https://via.placeholder.com/400x400/f4f2f1/161412?text=Mozzarella+Sticks',
    available: false
  },
  {
    id: '10',
    slug: 'new-york-cheesecake',
    name: 'New York Cheesecake',
    category: 'Desserts',
    description: 'Creamy cheesecake with a graham cracker crust, topped with fresh berries.',
    price: 8.99,
    image: 'https://via.placeholder.com/400x400/f4f2f1/161412?text=NY+Cheesecake',
    available: true
  },
  {
    id: '11',
    slug: 'garlic-bread',
    name: 'Garlic Bread',
    category: 'Appetizers',
    description: 'Toasted bread with garlic butter and herbs.',
    price: 4.99,
    image: 'https://via.placeholder.com/400x400/f4f2f1/161412?text=Garlic+Bread',
    available: true
  },
  {
    id: '12',
    slug: 'ribeye-steak',
    name: 'Grilled Ribeye Steak',
    category: 'Main Courses',
    description: '12 oz ribeye steak grilled to perfection with seasonal vegetables.',
    price: 24.99,
    image: 'https://via.placeholder.com/400x400/f4f2f1/161412?text=Ribeye+Steak',
    available: true
  }
];

// Helper function to find menu item by slug
export const getMenuItemBySlug = (slug: string): MenuItem | undefined => {
  return menuItems.find(item => item.slug === slug);
};

export default menuItems;
