/**
 * Sample table images for demonstration
 * These are high-quality restaurant/dining images that can be used as table backgrounds
 */

export const SAMPLE_TABLE_IMAGES = [
  // Elegant dining tables
  'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&crop=center',
  
  // Cozy restaurant interiors
  'https://images.unsplash.com/photo-1552566626-52f8b828add9?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1559329007-40df8a9345d8?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1590846406792-0adc7f938f1d?w=400&h=300&fit=crop&crop=center',
  
  // Modern dining spaces
  'https://images.unsplash.com/photo-1571997478779-2adcbbe9ab2f?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1544148103-0773bf10d330?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1592861956120-e524fc739696?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1551632811-561732d1e306?w=400&h=300&fit=crop&crop=center',
  
  // Outdoor dining
  'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1481833761820-0509d3217039?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1466978913421-dad2ebd01d17?w=400&h=300&fit=crop&crop=center',
  'https://images.unsplash.com/photo-1424847651672-bf20a4b0982b?w=400&h=300&fit=crop&crop=center',
];

/**
 * Get a random sample table image
 */
export function getRandomTableImage(): string {
  return SAMPLE_TABLE_IMAGES[Math.floor(Math.random() * SAMPLE_TABLE_IMAGES.length)];
}

/**
 * Get a table image by index (useful for consistent assignment)
 */
export function getTableImageByIndex(index: number): string {
  return SAMPLE_TABLE_IMAGES[index % SAMPLE_TABLE_IMAGES.length];
}

/**
 * Get table image by table number (for consistent assignment based on table number)
 */
export function getTableImageByNumber(tableNumber: number): string {
  return getTableImageByIndex(tableNumber - 1);
}
