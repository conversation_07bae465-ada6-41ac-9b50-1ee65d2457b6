'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { 
  ArrowLeft,
  Download,
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  PieChart,
  Calculator,
  Calendar,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// Mock Chart Component
function Chart({ data, type, className }: { data: any[], type: string, className?: string }) {
  return (
    <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-64', className)}>
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📊</div>
        <p>{type} Chart</p>
        <p className="text-sm">Chart visualization would go here</p>
      </div>
    </div>
  );
}

interface FinancialMetric {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
}

interface ExpenseCategory {
  name: string;
  amount: string;
  percentage: number;
  change: string;
  trend: 'up' | 'down';
  color: string;
}

interface ProfitLossItem {
  category: string;
  amount: number;
  percentage: number;
  type: 'revenue' | 'expense';
}

export default function FinancialReportPage() {
  const t = useTranslations('reports');
  
  // State
  const [dateRange, setDateRange] = useState('30d');
  const [loading, setLoading] = useState(true);

  // Mock data
  const financialMetrics: FinancialMetric[] = [
    {
      label: 'Total Revenue',
      value: '$84,250.00',
      change: '+12.5%',
      trend: 'up',
      icon: <DollarSign className="w-5 h-5" />
    },
    {
      label: 'Total Expenses',
      value: '$52,180.00',
      change: '+8.3%',
      trend: 'up',
      icon: <CreditCard className="w-5 h-5" />
    },
    {
      label: 'Net Profit',
      value: '$32,070.00',
      change: '+18.7%',
      trend: 'up',
      icon: <TrendingUp className="w-5 h-5" />
    },
    {
      label: 'Profit Margin',
      value: '38.1%',
      change: '+2.4%',
      trend: 'up',
      icon: <Calculator className="w-5 h-5" />
    }
  ];

  const expenseCategories: ExpenseCategory[] = [
    { name: 'Food & Beverages', amount: '$28,450', percentage: 54.5, change: '+5.2%', trend: 'up', color: 'bg-blue-500' },
    { name: 'Staff Salaries', amount: '$15,200', percentage: 29.1, change: '+3.1%', trend: 'up', color: 'bg-green-500' },
    { name: 'Rent & Utilities', amount: '$4,800', percentage: 9.2, change: '+0.5%', trend: 'up', color: 'bg-yellow-500' },
    { name: 'Marketing', amount: '$2,100', percentage: 4.0, change: '-12.3%', trend: 'down', color: 'bg-purple-500' },
    { name: 'Equipment', amount: '$980', percentage: 1.9, change: '+45.2%', trend: 'up', color: 'bg-red-500' },
    { name: 'Other', amount: '$650', percentage: 1.3, change: '-8.7%', trend: 'down', color: 'bg-gray-500' }
  ];

  const profitLossData: ProfitLossItem[] = [
    { category: 'Food Sales', amount: 68500, percentage: 81.3, type: 'revenue' },
    { category: 'Beverage Sales', amount: 12800, percentage: 15.2, type: 'revenue' },
    { category: 'Other Revenue', amount: 2950, percentage: 3.5, type: 'revenue' },
    { category: 'Cost of Goods Sold', amount: -28450, percentage: 33.8, type: 'expense' },
    { category: 'Labor Costs', amount: -15200, percentage: 18.0, type: 'expense' },
    { category: 'Operating Expenses', amount: -8530, percentage: 10.1, type: 'expense' }
  ];

  const cashFlowData = [
    { month: 'Jan', inflow: 75000, outflow: -48000, net: 27000 },
    { month: 'Feb', inflow: 82000, outflow: -52000, net: 30000 },
    { month: 'Mar', inflow: 78000, outflow: -49000, net: 29000 },
    { month: 'Apr', inflow: 85000, outflow: -53000, net: 32000 },
    { month: 'May', inflow: 88000, outflow: -55000, net: 33000 },
    { month: 'Jun', inflow: 84250, outflow: -52180, net: 32070 }
  ];

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link
            href="/app/reports"
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div className="flex items-center gap-3">
            <Calculator className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Financial Report</h1>
              <p className="text-gray-600">P&L, expenses, and financial performance</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {dateRangeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {financialMetrics.map((metric, index) => (
          <div key={index} className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 text-blue-600 rounded-lg">
                  {metric.icon}
                </div>
                <div>
                  <p className="text-sm text-gray-600">{metric.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                </div>
              </div>
              <div className={cn(
                'flex items-center gap-1 text-sm font-medium',
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
              )}>
                {metric.trend === 'up' ? (
                  <TrendingUp className="w-4 h-4" />
                ) : metric.trend === 'down' ? (
                  <TrendingDown className="w-4 h-4" />
                ) : null}
                {metric.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue vs Expenses */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Revenue vs Expenses</h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4" />
              Last 6 months
            </div>
          </div>
          <Chart data={cashFlowData} type="Bar" />
        </div>

        {/* Cash Flow */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Cash Flow Trend</h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <TrendingUp className="w-4 h-4" />
              Monthly
            </div>
          </div>
          <Chart data={cashFlowData} type="Line" />
        </div>
      </div>

      {/* Expense Breakdown */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Expense Breakdown</h3>
          <p className="text-gray-600">Operating expenses by category</p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {expenseCategories.map((category, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className={cn('w-4 h-4 rounded-full', category.color)}></div>
                    <h4 className="font-medium text-gray-900">{category.name}</h4>
                  </div>
                  <div className={cn(
                    'flex items-center gap-1 text-sm font-medium',
                    category.trend === 'up' ? 'text-red-600' : 'text-green-600'
                  )}>
                    {category.trend === 'up' ? (
                      <TrendingUp className="w-3 h-3" />
                    ) : (
                      <TrendingDown className="w-3 h-3" />
                    )}
                    {category.change}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Amount</span>
                    <span className="text-sm font-medium">{category.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Percentage</span>
                    <span className="text-sm font-medium">{category.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={cn('h-2 rounded-full', category.color)} 
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Profit & Loss Statement */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Profit & Loss Statement</h3>
          <p className="text-gray-600">Detailed income and expense breakdown</p>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  % of Revenue
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {/* Revenue Section */}
              <tr className="bg-green-50">
                <td className="px-6 py-3 text-sm font-semibold text-green-800">REVENUE</td>
                <td className="px-6 py-3 text-sm font-semibold text-green-800 text-right">
                  ${profitLossData.filter(item => item.type === 'revenue').reduce((sum, item) => sum + item.amount, 0).toLocaleString()}
                </td>
                <td className="px-6 py-3 text-sm font-semibold text-green-800 text-right">100.0%</td>
              </tr>
              {profitLossData.filter(item => item.type === 'revenue').map((item, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 pl-12">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    ${item.amount.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {item.percentage}%
                  </td>
                </tr>
              ))}
              
              {/* Expenses Section */}
              <tr className="bg-red-50">
                <td className="px-6 py-3 text-sm font-semibold text-red-800">EXPENSES</td>
                <td className="px-6 py-3 text-sm font-semibold text-red-800 text-right">
                  ${Math.abs(profitLossData.filter(item => item.type === 'expense').reduce((sum, item) => sum + item.amount, 0)).toLocaleString()}
                </td>
                <td className="px-6 py-3 text-sm font-semibold text-red-800 text-right">
                  {profitLossData.filter(item => item.type === 'expense').reduce((sum, item) => sum + item.percentage, 0).toFixed(1)}%
                </td>
              </tr>
              {profitLossData.filter(item => item.type === 'expense').map((item, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 pl-12">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    ${Math.abs(item.amount).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {item.percentage}%
                  </td>
                </tr>
              ))}
              
              {/* Net Profit */}
              <tr className="bg-blue-50 border-t-2 border-blue-200">
                <td className="px-6 py-4 text-sm font-bold text-blue-800">NET PROFIT</td>
                <td className="px-6 py-4 text-sm font-bold text-blue-800 text-right">
                  ${profitLossData.reduce((sum, item) => sum + item.amount, 0).toLocaleString()}
                </td>
                <td className="px-6 py-4 text-sm font-bold text-blue-800 text-right">
                  {((profitLossData.reduce((sum, item) => sum + item.amount, 0) / profitLossData.filter(item => item.type === 'revenue').reduce((sum, item) => sum + item.amount, 0)) * 100).toFixed(1)}%
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Financial Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Key Ratios */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Financial Ratios</h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Gross Profit Margin</span>
              <span className="text-sm font-medium">66.2%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Operating Margin</span>
              <span className="text-sm font-medium">38.1%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Food Cost %</span>
              <span className="text-sm font-medium">33.8%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Labor Cost %</span>
              <span className="text-sm font-medium">18.0%</span>
            </div>
          </div>
        </div>

        {/* Budget vs Actual */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Budget vs Actual</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-600">Revenue</span>
                <span className="text-sm font-medium text-green-600">+5.2%</span>
              </div>
              <div className="text-xs text-gray-500">$84,250 vs $80,000 budget</div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-600">Expenses</span>
                <span className="text-sm font-medium text-red-600">+8.3%</span>
              </div>
              <div className="text-xs text-gray-500">$52,180 vs $48,000 budget</div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-600">Net Profit</span>
                <span className="text-sm font-medium text-green-600">+0.2%</span>
              </div>
              <div className="text-xs text-gray-500">$32,070 vs $32,000 budget</div>
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Alerts</h3>
          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-800">Food costs increasing</p>
                <p className="text-xs text-yellow-600">Up 5.2% from last month</p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
              <TrendingUp className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-800">Revenue target exceeded</p>
                <p className="text-xs text-green-600">5.2% above monthly goal</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
