'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { 
  ArrowLeft,
  Download,
  Users,
  UserPlus,
  UserCheck,
  Heart,
  TrendingUp,
  TrendingDown,
  Calendar,
  MapPin,
  Star,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// Mock Chart Component
function Chart({ data, type, className }: { data: any[], type: string, className?: string }) {
  return (
    <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-64', className)}>
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📊</div>
        <p>{type} Chart</p>
        <p className="text-sm">Chart visualization would go here</p>
      </div>
    </div>
  );
}

interface CustomerMetric {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
}

interface CustomerSegment {
  name: string;
  count: number;
  percentage: number;
  avgSpend: string;
  color: string;
}

interface TopCustomer {
  name: string;
  email: string;
  orders: number;
  totalSpent: string;
  lastVisit: string;
  status: 'vip' | 'regular' | 'new';
}

export default function CustomerAnalyticsPage() {
  const t = useTranslations('reports');
  
  // State
  const [dateRange, setDateRange] = useState('30d');
  const [loading, setLoading] = useState(true);

  // Mock data
  const customerMetrics: CustomerMetric[] = [
    {
      label: 'Total Customers',
      value: '2,847',
      change: '+15.3%',
      trend: 'up',
      icon: <Users className="w-5 h-5" />
    },
    {
      label: 'New Customers',
      value: '234',
      change: '+22.1%',
      trend: 'up',
      icon: <UserPlus className="w-5 h-5" />
    },
    {
      label: 'Returning Customers',
      value: '1,456',
      change: '+8.7%',
      trend: 'up',
      icon: <UserCheck className="w-5 h-5" />
    },
    {
      label: 'Customer Retention',
      value: '68.5%',
      change: '+3.2%',
      trend: 'up',
      icon: <Heart className="w-5 h-5" />
    }
  ];

  const customerSegments: CustomerSegment[] = [
    { name: 'VIP Customers', count: 142, percentage: 5, avgSpend: '$125.50', color: 'bg-purple-500' },
    { name: 'Regular Customers', count: 1456, percentage: 51, avgSpend: '$45.20', color: 'bg-blue-500' },
    { name: 'Occasional Customers', count: 987, percentage: 35, avgSpend: '$28.75', color: 'bg-green-500' },
    { name: 'New Customers', count: 262, percentage: 9, avgSpend: '$32.10', color: 'bg-yellow-500' }
  ];

  const topCustomers: TopCustomer[] = [
    {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      orders: 47,
      totalSpent: '$2,340.50',
      lastVisit: '2 days ago',
      status: 'vip'
    },
    {
      name: 'Michael Chen',
      email: '<EMAIL>',
      orders: 32,
      totalSpent: '$1,876.25',
      lastVisit: '1 week ago',
      status: 'vip'
    },
    {
      name: 'Emily Davis',
      email: '<EMAIL>',
      orders: 28,
      totalSpent: '$1,456.75',
      lastVisit: '3 days ago',
      status: 'regular'
    },
    {
      name: 'David Wilson',
      email: '<EMAIL>',
      orders: 24,
      totalSpent: '$1,234.00',
      lastVisit: '5 days ago',
      status: 'regular'
    },
    {
      name: 'Lisa Anderson',
      email: '<EMAIL>',
      orders: 19,
      totalSpent: '$987.50',
      lastVisit: '1 day ago',
      status: 'regular'
    }
  ];

  const acquisitionData = [
    { source: 'Direct', customers: 456, percentage: 32 },
    { source: 'Social Media', customers: 342, percentage: 24 },
    { source: 'Referrals', customers: 298, percentage: 21 },
    { source: 'Search', customers: 187, percentage: 13 },
    { source: 'Email', customers: 143, percentage: 10 }
  ];

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ];

  const getStatusBadge = (status: TopCustomer['status']) => {
    switch (status) {
      case 'vip':
        return <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-700 rounded-full">VIP</span>;
      case 'regular':
        return <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-full">Regular</span>;
      case 'new':
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-700 rounded-full">New</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link
            href="/app/reports"
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Customer Analytics</h1>
              <p className="text-gray-600">Customer behavior and demographics insights</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {dateRangeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {customerMetrics.map((metric, index) => (
          <div key={index} className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 text-blue-600 rounded-lg">
                  {metric.icon}
                </div>
                <div>
                  <p className="text-sm text-gray-600">{metric.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                </div>
              </div>
              <div className={cn(
                'flex items-center gap-1 text-sm font-medium',
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
              )}>
                {metric.trend === 'up' ? (
                  <TrendingUp className="w-4 h-4" />
                ) : metric.trend === 'down' ? (
                  <TrendingDown className="w-4 h-4" />
                ) : null}
                {metric.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Growth */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Customer Growth</h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4" />
              Last 30 days
            </div>
          </div>
          <Chart data={[]} type="Line" />
        </div>

        {/* Customer Acquisition */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Acquisition Sources</h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <UserPlus className="w-4 h-4" />
              This month
            </div>
          </div>
          <div className="space-y-4">
            {acquisitionData.map((source, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">{source.source}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900">{source.customers}</span>
                  <span className="text-sm text-gray-500">({source.percentage}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Customer Segments */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Customer Segments</h3>
          <p className="text-gray-600">Customer distribution by engagement level</p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {customerSegments.map((segment, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <div className={cn('w-4 h-4 rounded-full', segment.color)}></div>
                  <h4 className="font-medium text-gray-900">{segment.name}</h4>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Count</span>
                    <span className="text-sm font-medium">{segment.count.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Percentage</span>
                    <span className="text-sm font-medium">{segment.percentage}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Avg Spend</span>
                    <span className="text-sm font-medium">{segment.avgSpend}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Customers */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Top Customers</h3>
          <p className="text-gray-600">Highest value customers by total spend</p>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Spent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Visit
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {topCustomers.map((customer, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                        {customer.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                        <div className="text-sm text-gray-500">{customer.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {customer.orders}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {customer.totalSpent}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.lastVisit}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(customer.status)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Customer Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Demographics */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Demographics</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600">Age 18-25</span>
                <span className="text-sm font-medium">18%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '18%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600">Age 26-35</span>
                <span className="text-sm font-medium">35%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '35%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600">Age 36-50</span>
                <span className="text-sm font-medium">32%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '32%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span className="text-sm text-gray-600">Age 50+</span>
                <span className="text-sm font-medium">15%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '15%' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Visit Frequency */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Visit Frequency</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Daily</span>
              <span className="text-sm font-medium">8%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Weekly</span>
              <span className="text-sm font-medium">25%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Monthly</span>
              <span className="text-sm font-medium">42%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Occasionally</span>
              <span className="text-sm font-medium">25%</span>
            </div>
          </div>
        </div>

        {/* Satisfaction */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Satisfaction</h3>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 mb-2">4.6</div>
              <div className="flex items-center justify-center gap-1 mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={cn(
                      'w-5 h-5',
                      star <= 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    )}
                  />
                ))}
              </div>
              <p className="text-sm text-gray-600">Average rating</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">5 stars</span>
                <span className="font-medium">68%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">4 stars</span>
                <span className="font-medium">22%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">3 stars</span>
                <span className="font-medium">7%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">2 stars</span>
                <span className="font-medium">2%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">1 star</span>
                <span className="font-medium">1%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
