'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { AppLoading } from '@/components/ui/app-loading';
import { ShoppingCart, Package, BarChart, Users, Settings, Plus, Search, Truck } from 'lucide-react';
import {
  useGetConvenienceProductsQuery,
  useGetConvenienceStatsQuery,
  useGetConvenienceOrdersQuery,
  useGetConvenienceSuppliersQuery
} from '@/lib/redux/api/endpoints/convenienceApi';
import { useSession } from 'next-auth/react';



export default function ConveniencePage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { data: session } = useSession();

  // Get merchant ID from session
  const merchantId = session?.user?.id || 'default-merchant-id';

  // Fetch real convenience store data
  const {
    data: convenienceProducts,
    isLoading: isLoadingProducts,
    isError: isErrorProducts
  } = useGetConvenienceProductsQuery({ merchantId });

  const {
    data: convenienceStats,
    isLoading: isLoadingStats,
    isError: isErrorStats
  } = useGetConvenienceStatsQuery({ merchantId });

  const {
    data: convenienceOrdersData,
    isLoading: isLoadingOrders,
    isError: isErrorOrders
  } = useGetConvenienceOrdersQuery({ merchantId, limit: 10 });

  const {
    data: convenienceSuppliers,
    isLoading: isLoadingSuppliers,
    isError: isErrorSuppliers
  } = useGetConvenienceSuppliersQuery({ merchantId });

  // Show loading state
  if (isLoadingProducts || isLoadingStats || isLoadingOrders || isLoadingSuppliers) {
    return <AppLoading />;
  }

  // Show error state if any API fails
  if (isErrorProducts || isErrorStats || isErrorOrders || isErrorSuppliers) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center text-red-600">
          <p>Error loading convenience store data. Please check your backend connection.</p>
          <p className="text-sm mt-2">
            {isErrorProducts && "Products API failed. "}
            {isErrorStats && "Stats API failed. "}
            {isErrorOrders && "Orders API failed. "}
            {isErrorSuppliers && "Suppliers API failed. "}
          </p>
        </div>
      </div>
    );
  }

  // Use real data from API
  const products = convenienceProducts || [];
  const orders = convenienceOrdersData?.data || [];
  const suppliers = convenienceSuppliers || [];
  const stats = convenienceStats || {
    totalProducts: products.length,
    totalOrders: orders.length,
    totalRevenue: 0,
    lowStockItems: products.filter(p => p.status === 'Low Stock' || p.status === 'Out of Stock').length
  };

  // Store information (this could come from merchant data in the future)
  const storeInfo = {
    name: 'Convenience Store',
    description: 'Your neighborhood convenience store'
  };

  return (
    <div className="font-be-vietnam">
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div>
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">{storeInfo.name}</h1>
          <p className="text-[#8a745c] text-sm">{storeInfo.description}</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border-none"
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="dashboard"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Dashboard</p>
            </TabsTrigger>
            <TabsTrigger
              value="inventory"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Inventory</p>
            </TabsTrigger>
            <TabsTrigger
              value="orders"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Orders</p>
            </TabsTrigger>
            <TabsTrigger
              value="suppliers"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Suppliers</p>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Dashboard Tab Content */}
        <TabsContent value="dashboard" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <Package className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">{stats.totalProducts}</p>
                <p className="text-[#8a745c] text-sm">{new Set(products.map(p => p.category)).size} categories</p>
              </CardContent>
            </Card>
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <ShoppingCart className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">{stats.totalOrders}</p>
                <p className="text-[#8a745c] text-sm">{orders.filter(o => o.status === 'Completed').length} completed</p>
              </CardContent>
            </Card>
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <BarChart className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">
                  ฿{stats.totalRevenue ? stats.totalRevenue.toLocaleString() : '0'}
                </p>
                <p className="text-[#8a745c] text-sm">From {stats.totalOrders} orders</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Recent Transactions</CardTitle>
                <CardDescription className="text-[#8a745c]">Latest store sales</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-[#e5e1dc]">
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Order ID</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Items</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Time</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {orders.slice(0, 3).map((order) => (
                        <tr key={order.id} className="border-b border-[#e5e1dc]">
                          <td className="py-3 text-[#181510]">{order.id}</td>
                          <td className="py-3 text-[#181510]">{order.items} items</td>
                          <td className="py-3 text-[#181510]">{order.time}</td>
                          <td className="py-3 text-[#181510]">{order.total}</td>
                        </tr>
                      ))}
                      {orders.length === 0 && (
                        <tr>
                          <td colSpan={4} className="py-6 text-center text-[#8a745c]">
                            No recent transactions
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                  <div className="mt-4 text-right">
                    <Link href="/app/convenience/orders" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      View all transactions →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Low Stock Items</CardTitle>
                <CardDescription className="text-[#8a745c]">Products that need restocking</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {products.filter(p => p.status === 'Low Stock' || p.status === 'Out of Stock').map((product) => (
                    <div key={product.id} className="flex items-center justify-between border-b border-[#e5e1dc] pb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-md overflow-hidden mr-3">
                          <img src={product.image} alt={product.name} className="w-full h-full object-cover" />
                        </div>
                        <div>
                          <p className="text-[#181510] font-medium">{product.name}</p>
                          <p className="text-[#8a745c] text-xs">{product.category} • ฿{typeof product.price === 'number' ? product.price : product.price}</p>
                        </div>
                      </div>
                      <div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          product.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {product.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 text-right">
                    <Link href="/app/convenience/inventory" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      Manage inventory →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Inventory Tab Content */}
        <TabsContent value="inventory" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search inventory..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#e5e1dc]">
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Product</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Category</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Price</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Stock</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Status</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product) => (
                      <tr key={product.id} className="border-b border-[#e5e1dc] hover:bg-[#f1edea]">
                        <td className="p-4">
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-md overflow-hidden mr-3">
                              <img src={product.image} alt={product.name} className="w-full h-full object-cover" />
                            </div>
                            <span className="text-[#181510]">{product.name}</span>
                          </div>
                        </td>
                        <td className="p-4 text-[#181510]">{product.category}</td>
                        <td className="p-4 text-[#181510]">฿{typeof product.price === 'number' ? product.price : product.price}</td>
                        <td className="p-4 text-[#181510]">{product.stock}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            product.status === 'In Stock' ? 'bg-green-100 text-green-800' :
                            product.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {product.status}
                          </span>
                        </td>
                        <td className="p-4">
                          <div className="flex space-x-2">
                            <Link href={`/app/convenience/inventory/${product.id}`} className="text-[#8a745c] hover:text-[#6d5a48]">
                              Edit
                            </Link>
                            <button className="text-[#8a745c] hover:text-[#6d5a48]">
                              Restock
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Orders Tab Content */}
        <TabsContent value="orders" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search orders..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              New Transaction
            </Button>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#e5e1dc]">
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Order ID</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Items</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Date</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Time</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Status</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Total</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {orders.map((order) => (
                      <tr key={order.id} className="border-b border-[#e5e1dc] hover:bg-[#f1edea]">
                        <td className="p-4 text-[#181510]">{order.id}</td>
                        <td className="p-4 text-[#181510]">{order.items} items</td>
                        <td className="p-4 text-[#181510]">{order.date}</td>
                        <td className="p-4 text-[#181510]">{order.time}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            order.status === 'Completed' ? 'bg-green-100 text-green-800' :
                            order.status === 'Processing' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="p-4 text-[#181510]">{order.total}</td>
                        <td className="p-4">
                          <Link href={`/app/convenience/orders/${order.id}`} className="text-[#8a745c] hover:text-[#6d5a48]">
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                    {orders.length === 0 && (
                      <tr>
                        <td colSpan={7} className="p-8 text-center text-[#8a745c]">
                          No orders found. Start by creating your first transaction.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Suppliers Tab Content */}
        <TabsContent value="suppliers" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search suppliers..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Supplier
            </Button>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#e5e1dc]">
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Supplier</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Category</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Last Delivery</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Next Delivery</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {suppliers.map((supplier) => (
                      <tr key={supplier.id} className="border-b border-[#e5e1dc] hover:bg-[#f1edea]">
                        <td className="p-4 text-[#181510] font-medium">{supplier.name}</td>
                        <td className="p-4 text-[#181510]">{supplier.category}</td>
                        <td className="p-4 text-[#181510]">{supplier.lastDelivery}</td>
                        <td className="p-4 text-[#181510]">{supplier.nextDelivery}</td>
                        <td className="p-4">
                          <div className="flex space-x-3">
                            <Link href={`/app/convenience/suppliers/${supplier.id}`} className="text-[#8a745c] hover:text-[#6d5a48]">
                              View
                            </Link>
                            <button className="text-[#8a745c] hover:text-[#6d5a48] flex items-center">
                              <Truck className="h-3 w-3 mr-1" />
                              Order
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                    {suppliers.length === 0 && (
                      <tr>
                        <td colSpan={5} className="p-8 text-center text-[#8a745c]">
                          No suppliers found. Add suppliers to manage your inventory.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
