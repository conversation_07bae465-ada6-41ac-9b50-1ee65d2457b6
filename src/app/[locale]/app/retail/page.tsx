'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ShoppingBag, Package, BarChart, Users, Settings, Plus, Search } from 'lucide-react';

// Mock retail store data
const retailStoreData = {
  name: 'Fashion Boutique',
  description: 'Premium clothing and accessories store',
  address: '123 Fashion Street, Bangkok, Thailand',
  phone: '+66 2 123 4567',
  email: '<EMAIL>',
  website: 'www.fashionboutique.com',
  openingHours: 'Mon-Sat: 10:00 AM - 9:00 PM, Sun: 11:00 AM - 8:00 PM',
};

// Mock products data
const mockProducts = [
  {
    id: '1',
    name: 'Premium T-Shirt',
    category: 'Clothing',
    price: '฿590',
    stock: 45,
    status: 'In Stock',
    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=2080&auto=format&fit=crop'
  },
  {
    id: '2',
    name: 'Designer Jeans',
    category: 'Clothing',
    price: '฿1,890',
    stock: 23,
    status: 'In Stock',
    image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?q=80&w=2026&auto=format&fit=crop'
  },
  {
    id: '3',
    name: 'Leather Handbag',
    category: 'Accessories',
    price: '฿2,490',
    stock: 12,
    status: 'Low Stock',
    image: 'https://images.unsplash.com/photo-1590874103328-eac38a683ce7?q=80&w=2076&auto=format&fit=crop'
  },
  {
    id: '4',
    name: 'Summer Dress',
    category: 'Clothing',
    price: '฿1,290',
    stock: 0,
    status: 'Out of Stock',
    image: 'https://images.unsplash.com/photo-1612336307429-8a898d10e223?q=80&w=1974&auto=format&fit=crop'
  },
  {
    id: '5',
    name: 'Sunglasses',
    category: 'Accessories',
    price: '฿890',
    stock: 34,
    status: 'In Stock',
    image: 'https://images.unsplash.com/photo-1511499767150-a48a237f0083?q=80&w=1980&auto=format&fit=crop'
  },
  {
    id: '6',
    name: 'Sneakers',
    category: 'Footwear',
    price: '฿2,190',
    stock: 18,
    status: 'In Stock',
    image: 'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?q=80&w=1974&auto=format&fit=crop'
  },
];

// Mock orders data
const mockOrders = [
  { id: 'ORD-001', customer: 'John Doe', date: '2023-10-15', status: 'Delivered', total: '฿1,480' },
  { id: 'ORD-002', customer: 'Jane Smith', date: '2023-10-14', status: 'Processing', total: '฿2,780' },
  { id: 'ORD-003', customer: 'Robert Johnson', date: '2023-10-14', status: 'Shipped', total: '฿3,090' },
  { id: 'ORD-004', customer: 'Emily Davis', date: '2023-10-13', status: 'Processing', total: '฿890' },
];

export default function RetailPage() {
  const [activeTab, setActiveTab] = useState('dashboard');

  return (
    <div className="font-be-vietnam">
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div>
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">{retailStoreData.name}</h1>
          <p className="text-[#8a745c] text-sm">{retailStoreData.description}</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border-none"
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="dashboard"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Dashboard</p>
            </TabsTrigger>
            <TabsTrigger
              value="products"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Products</p>
            </TabsTrigger>
            <TabsTrigger
              value="orders"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Orders</p>
            </TabsTrigger>
            <TabsTrigger
              value="customers"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Customers</p>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Dashboard Tab Content */}
        <TabsContent value="dashboard" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <Package className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">86</p>
                <p className="text-[#8a745c] text-sm">6 categories</p>
              </CardContent>
            </Card>
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <ShoppingBag className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">124</p>
                <p className="text-[#8a745c] text-sm">24 this week</p>
              </CardContent>
            </Card>
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <BarChart className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">฿45,890</p>
                <p className="text-[#8a745c] text-sm">+12% from last month</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Recent Orders</CardTitle>
                <CardDescription className="text-[#8a745c]">Latest customer purchases</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-[#e5e1dc]">
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Order ID</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Customer</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Status</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockOrders.slice(0, 3).map((order) => (
                        <tr key={order.id} className="border-b border-[#e5e1dc]">
                          <td className="py-3 text-[#181510]">{order.id}</td>
                          <td className="py-3 text-[#181510]">{order.customer}</td>
                          <td className="py-3">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              order.status === 'Delivered' ? 'bg-green-100 text-green-800' : 
                              order.status === 'Shipped' ? 'bg-blue-100 text-blue-800' : 
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="py-3 text-[#181510]">{order.total}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  <div className="mt-4 text-right">
                    <Link href="/app/retail/orders" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      View all orders →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Low Stock Products</CardTitle>
                <CardDescription className="text-[#8a745c]">Items that need restocking</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockProducts.filter(p => p.status === 'Low Stock' || p.status === 'Out of Stock').map((product) => (
                    <div key={product.id} className="flex items-center justify-between border-b border-[#e5e1dc] pb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-md overflow-hidden mr-3">
                          <img src={product.image} alt={product.name} className="w-full h-full object-cover" />
                        </div>
                        <div>
                          <p className="text-[#181510] font-medium">{product.name}</p>
                          <p className="text-[#8a745c] text-xs">{product.category}</p>
                        </div>
                      </div>
                      <div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          product.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' : 
                          'bg-red-100 text-red-800'
                        }`}>
                          {product.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 text-right">
                    <Link href="/app/retail/inventory" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      Manage inventory →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Products Tab Content */}
        <TabsContent value="products" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input 
                type="text" 
                placeholder="Search products..." 
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockProducts.map((product) => (
              <Link href={`/app/retail/products/${product.id}`} key={product.id}>
                <Card className="h-full overflow-hidden hover:shadow-md transition-shadow bg-[#fbfaf9] border-[#e5e1dc]">
                  <div 
                    className="h-40 bg-cover bg-center"
                    style={{ backgroundImage: `url(${product.image})` }}
                  />
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510]">{product.name}</CardTitle>
                        <CardDescription className="text-[#8a745c]">{product.category}</CardDescription>
                      </div>
                      <div className="text-[#181510] font-bold">{product.price}</div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <span className="text-[#8a745c] text-sm">Stock: {product.stock}</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        product.status === 'In Stock' ? 'bg-green-100 text-green-800' : 
                        product.status === 'Low Stock' ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-red-100 text-red-800'
                      }`}>
                        {product.status}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </TabsContent>

        {/* Orders Tab Content */}
        <TabsContent value="orders" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input 
                type="text" 
                placeholder="Search orders..." 
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#e5e1dc]">
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Order ID</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Customer</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Date</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Status</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Total</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mockOrders.map((order) => (
                      <tr key={order.id} className="border-b border-[#e5e1dc] hover:bg-[#f1edea]">
                        <td className="p-4 text-[#181510]">{order.id}</td>
                        <td className="p-4 text-[#181510]">{order.customer}</td>
                        <td className="p-4 text-[#181510]">{order.date}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            order.status === 'Delivered' ? 'bg-green-100 text-green-800' : 
                            order.status === 'Shipped' ? 'bg-blue-100 text-blue-800' : 
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="p-4 text-[#181510]">{order.total}</td>
                        <td className="p-4">
                          <Link href={`/app/retail/orders/${order.id}`} className="text-[#8a745c] hover:text-[#6d5a48]">
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customers Tab Content */}
        <TabsContent value="customers" className="mt-0">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <Users className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
              <h3 className="text-[#181510] text-lg font-medium mb-2">Customer Management</h3>
              <p className="text-[#8a745c] mb-4">Track and manage your customer relationships</p>
              <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
                Coming Soon
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
