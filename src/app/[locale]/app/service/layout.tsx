'use client';

import { NavigationProvider } from '@/lib/context/NavigationContext';
import { NavigationType } from '@/lib/types/navigation';
import { addIconsToNavItems } from '@/components/navigation/NavigationIcons';
import HeaderNavigation from '@/components/navigation/HeaderNavigation';
import SidebarNavigation from '@/components/navigation/SidebarNavigation';
import DrawerNavigation from '@/components/navigation/DrawerNavigation';
import BottomNavigation from '@/components/navigation/BottomNavigation';
import { useNavigation } from '@/lib/context/NavigationContext';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import NotificationPopover from '@/components/notifications/NotificationPopover';
import ProfileMenu from '@/components/navigation/ProfileMenu';

// Navigation wrapper component that uses the navigation context
function NavigationWrapper({ children }: { children: React.ReactNode }) {
  const { settings } = useNavigation();
  const [isCollapsed, setIsCollapsed] = useState(settings.isCollapsed || false);

  // Basic nav items without icons
  const basicNavItems = [
    { name: 'Shop Categories', href: '/app' },
    { name: 'Dashboard', href: '/app/service' },
    { name: 'Services', href: '/app/service/services' },
    { name: 'Appointments', href: '/app/service/appointments' },
    { name: 'Staff', href: '/app/service/staff' },
    { name: 'Clients', href: '/app/service/clients' },
    { name: 'Settings', href: '/app/service/settings' },
  ];

  // Add icons to nav items
  const navItems = addIconsToNavItems(basicNavItems);

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Render the appropriate navigation based on the settings
  const renderNavigation = () => {
    // Common header with search and profile
    const commonHeader = (
      <div className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f1edea] px-6 py-3">
        <div className="flex items-center gap-4 text-[#181510]">
          <div className="size-4">
            <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                fill="currentColor"
              ></path>
            </svg>
          </div>
          <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">Service Manager</h2>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative hidden md:block">
            <div className="flex w-full items-stretch rounded-lg h-10 bg-[#f1edea]">
              <div
                className="text-[#8a745c] flex border-none bg-[#f1edea] items-center justify-center pl-4 rounded-l-lg border-r-0"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                  ></path>
                </svg>
              </div>
              <input
                placeholder="Search"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-full placeholder:text-[#8a745c] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                defaultValue=""
              />
            </div>
          </div>
          <button className="md:hidden text-[#181510] hover:text-[#8a745c] transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
              ></path>
            </svg>
          </button>
          <NotificationPopover />
          <ProfileMenu />
        </div>
      </div>
    );

    switch (settings.type) {
      case NavigationType.HEADER:
        return (
          <HeaderNavigation
            navItems={navItems}
            showIcons={settings.showIcons}
          />
        );
      case NavigationType.SIDEBAR_LEFT:
        return (
          <>
            {commonHeader}
            <SidebarNavigation
              navItems={navItems}
              position="left"
              isCollapsed={isCollapsed}
              showIcons={settings.showIcons}
              showLabels={settings.showLabels}
              onToggleCollapse={handleToggleCollapse}
            />
          </>
        );
      case NavigationType.SIDEBAR_RIGHT:
        return (
          <>
            {commonHeader}
            <SidebarNavigation
              navItems={navItems}
              position="right"
              isCollapsed={isCollapsed}
              showIcons={settings.showIcons}
              showLabels={settings.showLabels}
              onToggleCollapse={handleToggleCollapse}
            />
          </>
        );
      case NavigationType.DRAWER:
        return (
          <>
            {commonHeader}
            <DrawerNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
            />
          </>
        );
      case NavigationType.BOTTOM:
        return (
          <>
            {commonHeader}
            <BottomNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
              showLabels={settings.showLabels}
            />
          </>
        );
      default:
        return (
          <HeaderNavigation
            navItems={navItems}
            showIcons={settings.showIcons}
          />
        );
    }
  };

  // Determine content class based on navigation type
  const getContentClass = () => {
    switch (settings.type) {
      case NavigationType.HEADER:
        return '';
      case NavigationType.SIDEBAR_LEFT:
        return isCollapsed ? 'ml-16 pt-16' : 'ml-64 pt-16';
      case NavigationType.SIDEBAR_RIGHT:
        return isCollapsed ? 'mr-16 pt-16' : 'mr-64 pt-16';
      case NavigationType.DRAWER:
        return 'pt-16';
      case NavigationType.BOTTOM:
        return 'pt-16 pb-16';
      default:
        return '';
    }
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] font-be-vietnam overflow-x-hidden">
      {renderNavigation()}
      <div
        className={cn(
          "flex-1 transition-all duration-300",
          getContentClass()
        )}
      >
        <div className=" flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1 ">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Main layout component that provides the navigation context
export default function ServiceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <NavigationProvider>
      <NavigationWrapper>
        {children}
      </NavigationWrapper>
    </NavigationProvider>
  );
}
