'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { AppLoading } from '@/components/ui/app-loading';
import { Download, BarChart, Users, Settings, Plus, Search, FileText, Monitor } from 'lucide-react';
import { useSession } from 'next-auth/react';
import {
  useGetDigitalProductsQuery,
  useGetDownloadStatsQuery,
  useGetLicensesQuery,
  useGetDownloadsQuery
} from '@/lib/redux/api/endpoints/digitalApi';

export default function DigitalPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { data: session } = useSession();

  // Get merchant ID from session
  const merchantId = session?.user?.id || 'default-merchant-id';

  // Fetch real digital products data
  const {
    data: digitalProductsData,
    isLoading: isLoadingProducts,
    isError: isErrorProducts
  } = useGetDigitalProductsQuery(merchantId);

  const {
    data: downloadStats,
    isLoading: isLoadingStats,
    isError: isErrorStats
  } = useGetDownloadStatsQuery(merchantId);

  const {
    data: licenses,
    isLoading: isLoadingLicenses,
    isError: isErrorLicenses
  } = useGetLicensesQuery({ merchantId });

  const {
    data: downloads,
    isLoading: isLoadingDownloads,
    isError: isErrorDownloads
  } = useGetDownloadsQuery({ merchantId });

  // Show loading state
  if (isLoadingProducts || isLoadingStats || isLoadingLicenses || isLoadingDownloads) {
    return <AppLoading />;
  }

  // Show error state if any API fails
  if (isErrorProducts || isErrorStats || isErrorLicenses || isErrorDownloads) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center text-red-600">
          <p>Error loading digital store data. Please check your backend connection.</p>
          <p className="text-sm mt-2">
            {isErrorProducts && "Products API failed. "}
            {isErrorStats && "Stats API failed. "}
            {isErrorLicenses && "Licenses API failed. "}
            {isErrorDownloads && "Downloads API failed. "}
          </p>
        </div>
      </div>
    );
  }

  // Use real data from API
  const products = digitalProductsData || [];
  const stats = downloadStats || { total: 0, today: 0, thisWeek: 0, thisMonth: 0 };
  const licenseList = licenses || [];
  const downloadList = downloads || [];

  // Store information (this could come from merchant data in the future)
  const storeInfo = {
    name: 'Digital Store',
    description: 'Premium digital products and online courses'
  };

  return (
    <div className="font-be-vietnam">
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div>
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">{storeInfo.name}</h1>
          <p className="text-[#8a745c] text-sm">{storeInfo.description}</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border-none"
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="dashboard"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Dashboard</p>
            </TabsTrigger>
            <TabsTrigger
              value="products"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Products</p>
            </TabsTrigger>
            <TabsTrigger
              value="orders"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Orders</p>
            </TabsTrigger>
            <TabsTrigger
              value="customers"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Customers</p>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Dashboard Tab Content */}
        <TabsContent value="dashboard" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <Monitor className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">{products.length}</p>
                <p className="text-[#8a745c] text-sm">{new Set(products.map(p => p.category)).size} categories</p>
              </CardContent>
            </Card>
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <Download className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Total Downloads
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">{stats.total}</p>
                <p className="text-[#8a745c] text-sm">{stats.thisMonth} this month</p>
              </CardContent>
            </Card>
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader className="pb-2">
                <CardTitle className="text-[#181510] text-lg flex items-center">
                  <BarChart className="mr-2 h-5 w-5 text-[#8a745c]" />
                  Active Licenses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-[#181510]">{licenseList.filter(l => l.status === 'active').length}</p>
                <p className="text-[#8a745c] text-sm">Total: {licenseList.length} licenses</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Recent Orders</CardTitle>
                <CardDescription className="text-[#8a745c]">Latest customer purchases</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-[#e5e1dc]">
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Order ID</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Customer</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Product</th>
                        <th className="text-left py-2 font-medium text-[#8a745c] text-sm">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {downloadList.slice(0, 3).map((download) => (
                        <tr key={download.id} className="border-b border-[#e5e1dc]">
                          <td className="py-3 text-[#181510]">{download.id}</td>
                          <td className="py-3 text-[#181510]">{download.userId}</td>
                          <td className="py-3 text-[#181510]">{products.find(p => p.id === download.productId)?.name || 'Unknown Product'}</td>
                          <td className="py-3 text-[#181510]">{download.successful ? 'Success' : 'Failed'}</td>
                        </tr>
                      ))}
                      {downloadList.length === 0 && (
                        <tr>
                          <td colSpan={4} className="py-6 text-center text-[#8a745c]">
                            No recent downloads
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                  <div className="mt-4 text-right">
                    <Link href="/app/digital/orders" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      View all orders →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Top Selling Products</CardTitle>
                <CardDescription className="text-[#8a745c]">Best performing digital products</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {products.slice(0, 3).map((product) => (
                    <div key={product.id} className="flex items-center justify-between border-b border-[#e5e1dc] pb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-md overflow-hidden mr-3">
                          <img
                            src={product.images?.[0] || 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?q=80&w=1964&auto=format&fit=crop'}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-[#181510] font-medium">{product.name}</p>
                          <p className="text-[#8a745c] text-xs">{product.category} • ฿{product.price}</p>
                        </div>
                      </div>
                      <div className="text-[#181510]">
                        {downloadList.filter(d => d.productId === product.id).length} downloads
                      </div>
                    </div>
                  ))}
                  {products.length === 0 && (
                    <div className="text-center text-[#8a745c] py-4">
                      No products available
                    </div>
                  )}
                  <div className="mt-4 text-right">
                    <Link href="/app/digital/products" className="text-[#8a745c] hover:text-[#6d5a48] text-sm">
                      View all products →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Products Tab Content */}
        <TabsContent value="products" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search products..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {products.map((product) => (
              <Link href={`/app/digital/products/${product.id}`} key={product.id}>
                <Card className="h-full overflow-hidden hover:shadow-md transition-shadow bg-[#fbfaf9] border-[#e5e1dc]">
                  <div
                    className="h-40 bg-cover bg-center"
                    style={{
                      backgroundImage: `url(${product.images?.[0] || 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?q=80&w=1964&auto=format&fit=crop'})`
                    }}
                  />
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510]">{product.name}</CardTitle>
                        <CardDescription className="text-[#8a745c]">{product.category}</CardDescription>
                      </div>
                      <div className="text-[#181510] font-bold">฿{product.price}</div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <span className="text-[#8a745c] text-sm">
                        {downloadList.filter(d => d.productId === product.id).length} downloads
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        product.available ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {product.available ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
            {products.length === 0 && (
              <div className="col-span-full text-center py-12">
                <Monitor className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
                <h3 className="text-[#181510] text-lg font-medium mb-2">No Digital Products</h3>
                <p className="text-[#8a745c] mb-4">Start by creating your first digital product</p>
                <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Product
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Orders Tab Content */}
        <TabsContent value="orders" className="mt-0">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <input
                type="text"
                placeholder="Search orders..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border-none bg-[#f1edea] text-[#181510] placeholder:text-[#8a745c] focus:outline-none focus:ring-2 focus:ring-[#e5ccb2]"
              />
            </div>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-[#e5e1dc]">
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Order ID</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Customer</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Product</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Date</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Status</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Total</th>
                      <th className="text-left p-4 font-medium text-[#8a745c] text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {licenseList.map((license) => (
                      <tr key={license.id} className="border-b border-[#e5e1dc] hover:bg-[#f1edea]">
                        <td className="p-4 text-[#181510]">{license.id}</td>
                        <td className="p-4 text-[#181510]">{license.userId}</td>
                        <td className="p-4 text-[#181510]">{products.find(p => p.id === license.productId)?.name || 'Unknown Product'}</td>
                        <td className="p-4 text-[#181510]">{new Date(license.activationDate).toLocaleDateString()}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            license.status === 'active' ? 'bg-green-100 text-green-800' :
                            license.status === 'expired' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {license.status}
                          </span>
                        </td>
                        <td className="p-4 text-[#181510]">{license.licenseKey}</td>
                        <td className="p-4">
                          <Link href={`/app/digital/licenses/${license.id}`} className="text-[#8a745c] hover:text-[#6d5a48]">
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                    {licenseList.length === 0 && (
                      <tr>
                        <td colSpan={7} className="p-8 text-center text-[#8a745c]">
                          No licenses found. Licenses will appear here when customers purchase your digital products.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customers Tab Content */}
        <TabsContent value="customers" className="mt-0">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <Users className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
              <h3 className="text-[#181510] text-lg font-medium mb-2">Customer Management</h3>
              <p className="text-[#8a745c] mb-4">Track and manage your customer relationships</p>
              <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
                Coming Soon
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
