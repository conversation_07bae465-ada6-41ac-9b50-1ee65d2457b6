'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  DollarSign, 
  Plus, 
  TrendingUp,
  TrendingDown,
  Calculator,
  CreditCard,
  Receipt,
  PieChart,
  BarChart3,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Download,
  Eye,
  Edit
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

// Mock Chart Component
function Chart({ data, type, className }: { data: any[], type: string, className?: string }) {
  return (
    <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-64', className)}>
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📊</div>
        <p>{type} Chart</p>
        <p className="text-sm">Chart visualization would go here</p>
      </div>
    </div>
  );
}

// Types
interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  profitMargin: number;
  grossMargin: number;
  operatingMargin: number;
  cashFlow: number;
  accountsReceivable: number;
  accountsPayable: number;
}

interface ExpenseCategory {
  name: string;
  amount: number;
  percentage: number;
  change: number;
  budget: number;
  color: string;
}

interface RevenueStream {
  name: string;
  amount: number;
  percentage: number;
  change: number;
  color: string;
}

interface FinancialAlert {
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  severity: 'high' | 'medium' | 'low';
}

export default function FinancePage() {
  const t = useTranslations('finance');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');

  // Mock data
  const mockMetrics: FinancialMetrics = {
    totalRevenue: 84250,
    totalExpenses: 52180,
    netProfit: 32070,
    profitMargin: 38.1,
    grossMargin: 66.2,
    operatingMargin: 42.5,
    cashFlow: 28450,
    accountsReceivable: 12500,
    accountsPayable: 8750,
  };

  const mockExpenseCategories: ExpenseCategory[] = [
    {
      name: 'Food & Beverages',
      amount: 28450,
      percentage: 54.5,
      change: 5.2,
      budget: 27000,
      color: 'bg-blue-500',
    },
    {
      name: 'Staff Salaries',
      amount: 15180,
      percentage: 29.1,
      change: 3.1,
      budget: 15000,
      color: 'bg-green-500',
    },
    {
      name: 'Rent & Utilities',
      amount: 4800,
      percentage: 9.2,
      change: 0.5,
      budget: 4800,
      color: 'bg-yellow-500',
    },
    {
      name: 'Marketing',
      amount: 2100,
      percentage: 4.0,
      change: -12.3,
      budget: 2400,
      color: 'bg-purple-500',
    },
    {
      name: 'Equipment',
      amount: 990,
      percentage: 1.9,
      change: 45.2,
      budget: 800,
      color: 'bg-red-500',
    },
    {
      name: 'Other',
      amount: 660,
      percentage: 1.3,
      change: -8.7,
      budget: 700,
      color: 'bg-gray-500',
    },
  ];

  const mockRevenueStreams: RevenueStream[] = [
    {
      name: 'Food Sales',
      amount: 68450,
      percentage: 81.3,
      change: 8.5,
      color: 'bg-blue-500',
    },
    {
      name: 'Beverage Sales',
      amount: 12800,
      percentage: 15.2,
      change: 12.3,
      color: 'bg-green-500',
    },
    {
      name: 'Delivery Fees',
      amount: 2100,
      percentage: 2.5,
      change: 25.8,
      color: 'bg-purple-500',
    },
    {
      name: 'Other Revenue',
      amount: 900,
      percentage: 1.0,
      change: -5.2,
      color: 'bg-yellow-500',
    },
  ];

  const mockAlerts: FinancialAlert[] = [
    {
      type: 'warning',
      title: 'Food costs increasing',
      message: 'Food & beverage costs are 5.2% above budget this month',
      severity: 'medium',
    },
    {
      type: 'info',
      title: 'Revenue target exceeded',
      message: 'Monthly revenue is 8.5% above target',
      severity: 'low',
    },
    {
      type: 'error',
      title: 'Equipment expenses spike',
      message: 'Equipment costs are 45% over budget due to new purchases',
      severity: 'high',
    },
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ];

  // Get alert icon
  const getAlertIcon = (type: FinancialAlert['type']) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'info':
        return <CheckCircle className="w-4 h-4 text-blue-600" />;
    }
  };

  // Get alert color
  const getAlertColor = (type: FinancialAlert['type']) => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <DollarSign className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Financial Dashboard</h1>
            <p className="text-gray-600">Monitor revenue, expenses, and profitability</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              {dateRangeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockMetrics.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-green-600">+12.5% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockMetrics.totalExpenses.toLocaleString()}</div>
            <p className="text-xs text-red-600">+8.3% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">${mockMetrics.netProfit.toLocaleString()}</div>
            <p className="text-xs text-green-600">+18.7% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
            <Calculator className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockMetrics.profitMargin}%</div>
            <p className="text-xs text-green-600">+2.4% from last month</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/finance/expenses`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Receipt className="h-8 w-8 text-red-600 mr-4" />
              <div>
                <h3 className="font-semibold">Expenses</h3>
                <p className="text-sm text-gray-600">Track spending</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/finance/payroll`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <CreditCard className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold">Payroll</h3>
                <p className="text-sm text-gray-600">Staff payments</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/finance/taxes`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Calculator className="h-8 w-8 text-green-600 mr-4" />
              <div>
                <h3 className="font-semibold">Taxes</h3>
                <p className="text-sm text-gray-600">Tax management</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/finance/reports`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <BarChart3 className="h-8 w-8 text-purple-600 mr-4" />
              <div>
                <h3 className="font-semibold">Reports</h3>
                <p className="text-sm text-gray-600">Financial analysis</p>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Financial Alerts */}
      {mockAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Financial Alerts</CardTitle>
            <p className="text-sm text-gray-600">Important financial notifications</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockAlerts.map((alert, index) => (
                <div key={index} className={cn('p-4 rounded-lg border', getAlertColor(alert.type))}>
                  <div className="flex items-start gap-3">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <h4 className="font-medium">{alert.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                    </div>
                    <Badge variant={alert.severity === 'high' ? 'destructive' : 'secondary'}>
                      {alert.severity}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue vs Expenses */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs Expenses</CardTitle>
            <p className="text-sm text-gray-600">Monthly comparison over time</p>
          </CardHeader>
          <CardContent>
            <Chart data={[]} type="Line" />
          </CardContent>
        </Card>

        {/* Profit Margin Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Profit Margin Trend</CardTitle>
            <p className="text-sm text-gray-600">Profitability over time</p>
          </CardHeader>
          <CardContent>
            <Chart data={[]} type="Area" />
          </CardContent>
        </Card>
      </div>

      {/* Expense Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Expense Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Expense Breakdown</CardTitle>
            <p className="text-sm text-gray-600">Spending by category</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockExpenseCategories.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className={cn('w-3 h-3 rounded-full', category.color)}></div>
                      <span className="font-medium">{category.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">${category.amount.toLocaleString()}</div>
                      <div className={cn(
                        'text-sm',
                        category.change > 0 ? 'text-red-600' : 'text-green-600'
                      )}>
                        {category.change > 0 ? '+' : ''}{category.change}%
                      </div>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <Progress value={category.percentage} className="h-2" />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{category.percentage}% of total</span>
                      <span>Budget: ${category.budget.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Revenue Streams */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Streams</CardTitle>
            <p className="text-sm text-gray-600">Income by source</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRevenueStreams.map((stream, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className={cn('w-3 h-3 rounded-full', stream.color)}></div>
                      <span className="font-medium">{stream.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">${stream.amount.toLocaleString()}</div>
                      <div className={cn(
                        'text-sm',
                        stream.change > 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {stream.change > 0 ? '+' : ''}{stream.change}%
                      </div>
                    </div>
                  </div>
                  <Progress value={stream.percentage} className="h-2" />
                  <div className="text-xs text-gray-500">
                    {stream.percentage}% of total revenue
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Financial Ratios */}
      <Card>
        <CardHeader>
          <CardTitle>Key Financial Ratios</CardTitle>
          <p className="text-sm text-gray-600">Important financial health indicators</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">
                {mockMetrics.grossMargin}%
              </div>
              <div className="text-sm font-medium mb-1">Gross Margin</div>
              <div className="text-xs text-gray-500">Revenue - COGS</div>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {mockMetrics.operatingMargin}%
              </div>
              <div className="text-sm font-medium mb-1">Operating Margin</div>
              <div className="text-xs text-gray-500">Operating income / Revenue</div>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                ${mockMetrics.cashFlow.toLocaleString()}
              </div>
              <div className="text-sm font-medium mb-1">Cash Flow</div>
              <div className="text-xs text-gray-500">Net cash position</div>
            </div>

            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 mb-2">
                1.4x
              </div>
              <div className="text-sm font-medium mb-1">Current Ratio</div>
              <div className="text-xs text-gray-500">Assets / Liabilities</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Accounts Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Accounts Receivable</CardTitle>
            <p className="text-sm text-gray-600">Money owed to you</p>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                ${mockMetrics.accountsReceivable.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 mb-4">Outstanding receivables</div>
              <div className="flex justify-center gap-4">
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </Button>
                <Button size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Manage
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Accounts Payable</CardTitle>
            <p className="text-sm text-gray-600">Money you owe</p>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <div className="text-3xl font-bold text-red-600 mb-2">
                ${mockMetrics.accountsPayable.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 mb-4">Outstanding payables</div>
              <div className="flex justify-center gap-4">
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </Button>
                <Button size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Pay Bills
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
