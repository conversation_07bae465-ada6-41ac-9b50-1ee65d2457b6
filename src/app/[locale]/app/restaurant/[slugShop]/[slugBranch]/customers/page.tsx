'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  Users, 
  Plus, 
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  ShoppingCart,
  Star,
  TrendingUp,
  UserPlus,
  Heart,
  Clock,
  RefreshCw,
  Download,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types
interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  dateOfBirth?: Date;
  joinDate: Date;
  lastVisit: Date;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  favoriteItems: string[];
  loyaltyTier?: 'bronze' | 'silver' | 'gold' | 'platinum';
  loyaltyPoints: number;
  status: 'active' | 'inactive' | 'vip';
  tags: string[];
  notes?: string;
  preferredContactMethod: 'email' | 'phone' | 'sms';
  marketingOptIn: boolean;
}

interface CustomerStats {
  totalCustomers: number;
  newCustomers: number;
  activeCustomers: number;
  vipCustomers: number;
  averageLifetimeValue: number;
  retentionRate: number;
}

export default function CustomersPage() {
  const t = useTranslations('customers');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [tierFilter, setTierFilter] = useState<string>('all');

  // Mock data
  const mockCustomers: Customer[] = [
    {
      id: '1',
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001'
      },
      dateOfBirth: new Date('1985-06-15'),
      joinDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      totalOrders: 47,
      totalSpent: 2340.50,
      averageOrderValue: 49.80,
      favoriteItems: ['Margherita Pizza', 'Caesar Salad', 'Tiramisu'],
      loyaltyTier: 'gold',
      loyaltyPoints: 2450,
      status: 'vip',
      tags: ['frequent-diner', 'pizza-lover'],
      notes: 'Prefers table by the window. Allergic to nuts.',
      preferredContactMethod: 'email',
      marketingOptIn: true,
    },
    {
      id: '2',
      firstName: 'Michael',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '+****************',
      joinDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      totalOrders: 89,
      totalSpent: 4156.25,
      averageOrderValue: 46.70,
      favoriteItems: ['Grilled Salmon', 'Pasta Carbonara', 'Chocolate Cake'],
      loyaltyTier: 'platinum',
      loyaltyPoints: 4200,
      status: 'vip',
      tags: ['high-value', 'seafood-lover'],
      preferredContactMethod: 'phone',
      marketingOptIn: true,
    },
    {
      id: '3',
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '+****************',
      joinDate: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      totalOrders: 28,
      totalSpent: 1456.75,
      averageOrderValue: 52.03,
      favoriteItems: ['Vegetarian Burger', 'Quinoa Salad'],
      loyaltyTier: 'silver',
      loyaltyPoints: 890,
      status: 'active',
      tags: ['vegetarian', 'health-conscious'],
      preferredContactMethod: 'email',
      marketingOptIn: true,
    },
    {
      id: '4',
      firstName: 'David',
      lastName: 'Wilson',
      email: '<EMAIL>',
      joinDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      totalOrders: 12,
      totalSpent: 567.80,
      averageOrderValue: 47.32,
      favoriteItems: ['Chicken Wings', 'Beer'],
      loyaltyTier: 'bronze',
      loyaltyPoints: 340,
      status: 'active',
      tags: ['casual-diner'],
      preferredContactMethod: 'sms',
      marketingOptIn: false,
    },
    {
      id: '5',
      firstName: 'Lisa',
      lastName: 'Anderson',
      email: '<EMAIL>',
      phone: '+****************',
      joinDate: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      totalOrders: 35,
      totalSpent: 1987.50,
      averageOrderValue: 56.79,
      favoriteItems: ['Steak', 'Red Wine', 'Cheesecake'],
      loyaltyTier: 'silver',
      loyaltyPoints: 1120,
      status: 'inactive',
      tags: ['wine-enthusiast', 'special-occasions'],
      preferredContactMethod: 'email',
      marketingOptIn: true,
    },
  ];

  const mockStats: CustomerStats = {
    totalCustomers: mockCustomers.length,
    newCustomers: mockCustomers.filter(c => 
      (Date.now() - c.joinDate.getTime()) < 30 * 24 * 60 * 60 * 1000
    ).length,
    activeCustomers: mockCustomers.filter(c => c.status === 'active' || c.status === 'vip').length,
    vipCustomers: mockCustomers.filter(c => c.status === 'vip').length,
    averageLifetimeValue: mockCustomers.reduce((sum, c) => sum + c.totalSpent, 0) / mockCustomers.length,
    retentionRate: 68.5,
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setCustomers(mockCustomers);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter customers
  const filteredCustomers = customers.filter(customer => {
    const fullName = `${customer.firstName} ${customer.lastName}`.toLowerCase();
    const matchesSearch = fullName.includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (customer.phone && customer.phone.includes(searchQuery));
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    const matchesTier = tierFilter === 'all' || customer.loyaltyTier === tierFilter;
    
    return matchesSearch && matchesStatus && matchesTier;
  });

  // Get status badge
  const getStatusBadge = (status: Customer['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: Users },
      inactive: { color: 'bg-gray-100 text-gray-800', icon: Clock },
      vip: { color: 'bg-purple-100 text-purple-800', icon: Star },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {status.toUpperCase()}
      </Badge>
    );
  };

  // Get loyalty tier badge
  const getTierBadge = (tier?: Customer['loyaltyTier']) => {
    if (!tier) return null;

    const tierConfig = {
      bronze: { color: 'bg-orange-100 text-orange-800' },
      silver: { color: 'bg-gray-100 text-gray-800' },
      gold: { color: 'bg-yellow-100 text-yellow-800' },
      platinum: { color: 'bg-purple-100 text-purple-800' },
    };

    const config = tierConfig[tier];

    return (
      <Badge className={config.color}>
        {tier.charAt(0).toUpperCase() + tier.slice(1)}
      </Badge>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 30) return `${days} days ago`;
    if (days < 365) return `${Math.floor(days / 30)} months ago`;
    return `${Math.floor(days / 365)} years ago`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Customer Management</h1>
            <p className="text-gray-600">Manage customer relationships and data</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/add`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Customer
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalCustomers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New This Month</CardTitle>
            <UserPlus className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{mockStats.newCustomers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Heart className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.activeCustomers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VIP</CardTitle>
            <Star className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{mockStats.vipCustomers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Lifetime Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockStats.averageLifetimeValue.toFixed(0)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.retentionRate}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/segments`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Users className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold">Customer Segments</h3>
                <p className="text-sm text-gray-600">Group customers by behavior</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/analytics`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <TrendingUp className="h-8 w-8 text-green-600 mr-4" />
              <div>
                <h3 className="font-semibold">Customer Analytics</h3>
                <p className="text-sm text-gray-600">Behavior insights</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/communication`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <MessageSquare className="h-8 w-8 text-purple-600 mr-4" />
              <div>
                <h3 className="font-semibold">Communication</h3>
                <p className="text-sm text-gray-600">Send messages & emails</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/feedback`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <Star className="h-8 w-8 text-yellow-600 mr-4" />
              <div>
                <h3 className="font-semibold">Feedback & Reviews</h3>
                <p className="text-sm text-gray-600">Customer satisfaction</p>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="vip">VIP</SelectItem>
          </SelectContent>
        </Select>

        <Select value={tierFilter} onValueChange={setTierFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by tier" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tiers</SelectItem>
            <SelectItem value="bronze">Bronze</SelectItem>
            <SelectItem value="silver">Silver</SelectItem>
            <SelectItem value="gold">Gold</SelectItem>
            <SelectItem value="platinum">Platinum</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Customers Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Customer</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Loyalty</TableHead>
              <TableHead>Orders</TableHead>
              <TableHead>Total Spent</TableHead>
              <TableHead>Last Visit</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCustomers.map((customer) => (
              <TableRow key={customer.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                      {customer.firstName[0]}{customer.lastName[0]}
                    </div>
                    <div>
                      <div className="font-medium">{customer.firstName} {customer.lastName}</div>
                      <div className="text-sm text-gray-500">
                        Member since {formatDate(customer.joinDate)}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="w-3 h-3 text-gray-400" />
                      {customer.email}
                    </div>
                    {customer.phone && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="w-3 h-3 text-gray-400" />
                        {customer.phone}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(customer.status)}</TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {getTierBadge(customer.loyaltyTier)}
                    <div className="text-sm text-gray-600">
                      {customer.loyaltyPoints.toLocaleString()} points
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{customer.totalOrders}</div>
                    <div className="text-sm text-gray-500">
                      ${customer.averageOrderValue.toFixed(2)} avg
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="font-medium">${customer.totalSpent.toLocaleString()}</div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {formatTimeAgo(customer.lastVisit)}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/${customer.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Profile
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/${customer.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Customer
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Send Message
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        View Orders
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Customer
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {filteredCustomers.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || statusFilter !== 'all' || tierFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start building your customer database'}
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/customers/add`}>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add First Customer
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
