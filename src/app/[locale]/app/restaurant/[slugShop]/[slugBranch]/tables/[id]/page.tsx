'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { ArrowLeft, QrCode, Users, Clock, Calendar, Edit, Camera } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { Button } from '@/components/ui/button';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetTableQuery } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { TableImageUpload } from '@/components/restaurant/TableImageUpload';

interface TableDetailPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    id: string;
  }>;
}

export default function TableDetailPage({ params }: TableDetailPageProps) {
  const { slugShop, slugBranch, id } = React.use(params);
  const [showImageUpload, setShowImageUpload] = useState(false);

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants, error: merchantsError } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  // Get table data from backend
  const {
    data: table,
    isLoading: isLoadingTable,
    error: tableError,
    refetch: refetchTable
  } = useGetTableQuery(
    {
      shopId: merchant?.id || '',
      branchId: branch?.id || '',
      tableId: id
    },
    {
      skip: !merchant?.id || !branch?.id
    }
  );

  const isLoading = isLoadingMerchants || isLoadingTable;
  const hasError = merchantsError || tableError;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Error Loading Data</h1>
        <p className="text-[#8a745c] text-sm">There was an error loading the table data. Please try again.</p>
      </div>
    );
  }

  if (!merchant || !branch || !table) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Table Not Found</h1>
        <p className="text-[#8a745c] text-sm">The table you are looking for does not exist.</p>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'reserved':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <>
      {/* Back Button */}
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`}>
          <button className="flex items-center gap-2 text-[#8a745c] hover:text-[#181510] transition-colors">
            <ArrowLeft size={20} />
            <span className="text-sm font-medium">Back to Tables</span>
          </button>
        </Link>
      </div>

      {/* Page Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">{table.name}</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Table details and management for {table.name}
          </p>
        </div>
        <div className="flex items-start gap-3">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(table.status)}`}>
            {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
          </span>
        </div>
      </div>

      {/* Table Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Table Image */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-[#181510] text-lg font-semibold">Table Image</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowImageUpload(!showImageUpload)}
              className="border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
            >
              <Camera className="w-4 h-4 mr-2" />
              {showImageUpload ? 'Hide Upload' : 'Change Image'}
            </Button>
          </div>

          {showImageUpload ? (
            <TableImageUpload
              shopId={merchant.id}
              branchId={branch.id}
              tableId={table.id}
              tableName={table.name}
              tableNumber={table.number.toString()}
              currentImageUrl={table.image_url}
              onImageUploaded={(imageUrl) => {
                refetchTable();
                setShowImageUpload(false);
              }}
            />
          ) : (
            <div
              className="w-full h-64 bg-cover bg-center rounded-lg border border-[#e2dcd4]"
              style={{
                backgroundImage: table.image_url
                  ? `url("${table.image_url}")`
                  : 'linear-gradient(135deg, #f1edea 0%, #e5ccb2 100%)'
              }}
            />
          )}
        </div>

        {/* Table Information */}
        <div className="space-y-4">
          <h2 className="text-[#181510] text-lg font-semibold">Table Information</h2>
          <div className="bg-white border border-[#e2dcd4] rounded-lg p-4 space-y-4">
            <div className="flex items-center gap-3">
              <Users size={20} className="text-[#8a745c]" />
              <div>
                <p className="text-sm text-[#8a745c]">Capacity</p>
                <p className="text-[#181510] font-medium">{table.capacity} guests</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Calendar size={20} className="text-[#8a745c]" />
              <div>
                <p className="text-sm text-[#8a745c]">Area</p>
                <p className="text-[#181510] font-medium">{table.area?.name || 'No Area Assigned'}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Edit size={20} className="text-[#8a745c]" />
              <div>
                <p className="text-sm text-[#8a745c]">Shape</p>
                <p className="text-[#181510] font-medium capitalize">{table.shape}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Clock size={20} className="text-[#8a745c]" />
              <div>
                <p className="text-sm text-[#8a745c]">Status</p>
                <p className="text-[#181510] font-medium capitalize">{table.status}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* QR Code Section */}
      <div className="bg-white border border-[#e2dcd4] rounded-lg p-6 mb-8">
        <h2 className="text-[#181510] text-lg font-semibold mb-4">QR Code</h2>
        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="flex-shrink-0">
            {table.qr_code_url ? (
              <img
                src={table.qr_code_url}
                alt={`QR Code for ${table.name}`}
                className="w-32 h-32 border border-[#e2dcd4] rounded-lg"
              />
            ) : (
              <div className="w-32 h-32 border border-[#e2dcd4] rounded-lg bg-[#f1edea] flex items-center justify-center">
                <QrCode size={32} className="text-[#8a745c]" />
              </div>
            )}
          </div>
          <div className="flex-1">
            <p className="text-[#8a745c] text-sm mb-4">
              Customers can scan this QR code to view the menu and place orders directly from their table.
            </p>
            <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d4b896]">
              <QrCode size={16} className="mr-2" />
              {table.qr_code_url ? 'Download QR Code' : 'Generate QR Code'}
            </Button>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button
          variant="outline"
          className="bg-[#f1edea] text-[#181510] border-[#e2dcd4] hover:bg-[#e2dcd4]"
        >
          <Edit className="w-4 h-4 mr-2" />
          Edit Table
        </Button>
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new?tableId=${table.id}`}>
          <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d4b896]">
            <Calendar className="w-4 h-4 mr-2" />
            Add Reservation
          </Button>
        </Link>
      </div>
    </>
  );
}
