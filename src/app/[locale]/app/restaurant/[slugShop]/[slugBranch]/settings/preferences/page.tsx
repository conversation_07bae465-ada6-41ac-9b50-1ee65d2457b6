'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useGetMerchantsQuery, useGetBranchSettingsQuery, useUpdateBranchSettingsMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';

interface PreferencesPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

interface RestaurantPreferences {
  restaurantName: string;
  restaurantAddress: string;
  contactPhone: string;
  contactEmail: string;
  openingHours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
  paymentMethods: {
    cash: boolean;
    creditCard: boolean;
    debitCard: boolean;
    mobilePayment: boolean;
    giftCard: boolean;
  };
  tableLayout: {
    numberOfTables: string;
    maximumPartySize: string;
  };
  onlineOrdering: 'enabled' | 'disabled';
}

export default function PreferencesPage({ params }: PreferencesPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  const merchantId = merchant?.id;
  const branchId = branch?.id;

  // Get branch settings
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
    isError: isSettingsError
  } = useGetBranchSettingsQuery(
    { shopId: merchantId || '', branchId: branchId || '' },
    { skip: !merchantId || !branchId }
  );

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  const [preferences, setPreferences] = useState<RestaurantPreferences>({
    restaurantName: '',
    restaurantAddress: '',
    contactPhone: '',
    contactEmail: '',
    openingHours: {
      monday: '9:00 AM - 10:00 PM',
      tuesday: '9:00 AM - 10:00 PM',
      wednesday: '9:00 AM - 10:00 PM',
      thursday: '9:00 AM - 10:00 PM',
      friday: '9:00 AM - 11:00 PM',
      saturday: '9:00 AM - 11:00 PM',
      sunday: '10:00 AM - 9:00 PM',
    },
    paymentMethods: {
      cash: true,
      creditCard: true,
      debitCard: true,
      mobilePayment: false,
      giftCard: false,
    },
    tableLayout: {
      numberOfTables: '20',
      maximumPartySize: '8',
    },
    onlineOrdering: 'enabled',
  });

  const isLoading = isLoadingMerchants || isLoadingSettings;

  // Load branch data and settings into form
  useEffect(() => {
    if (merchant && branch) {
      setPreferences(prev => ({
        ...prev,
        restaurantName: merchant.name,
        restaurantAddress: branch.address || '',
        contactPhone: branch.phoneNumber || '',
        contactEmail: branch.email || '',
      }));
    }

    if (branchSettings) {
      setPreferences(prev => ({
        ...prev,
        // Map business hours from settings
        openingHours: branchSettings.businessHours || prev.openingHours,
        // Map payment methods from settings
        paymentMethods: {
          cash: branchSettings.paymentMethods?.includes('cash') || false,
          creditCard: branchSettings.paymentMethods?.includes('credit_card') || false,
          debitCard: branchSettings.paymentMethods?.includes('debit_card') || false,
          mobilePayment: branchSettings.paymentMethods?.includes('mobile_payment') || false,
          giftCard: branchSettings.paymentMethods?.includes('gift_card') || false,
        },
        // Map features from settings
        onlineOrdering: branchSettings.onlineOrdering ? 'enabled' : 'disabled',
      }));
    }
  }, [merchant, branch, branchSettings]);

  const handleInputChange = (field: keyof RestaurantPreferences, value: string) => {
    setPreferences(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleOpeningHoursChange = (day: keyof RestaurantPreferences['openingHours'], value: string) => {
    setPreferences(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: value,
      },
    }));
  };

  const handlePaymentMethodChange = (method: keyof RestaurantPreferences['paymentMethods'], checked: boolean) => {
    setPreferences(prev => ({
      ...prev,
      paymentMethods: {
        ...prev.paymentMethods,
        [method]: checked,
      },
    }));
  };

  const handleTableLayoutChange = (field: keyof RestaurantPreferences['tableLayout'], value: string) => {
    setPreferences(prev => ({
      ...prev,
      tableLayout: {
        ...prev.tableLayout,
        [field]: value,
      },
    }));
  };

  const handleSavePreferences = async () => {
    if (!merchantId || !branchId) {
      toast.error('Missing merchant or branch information');
      return;
    }

    try {
      // Prepare payment methods array
      const paymentMethods = Object.entries(preferences.paymentMethods)
        .filter(([_, enabled]) => enabled)
        .map(([method, _]) => {
          switch (method) {
            case 'creditCard': return 'credit_card';
            case 'debitCard': return 'debit_card';
            case 'mobilePayment': return 'mobile_payment';
            case 'giftCard': return 'gift_card';
            default: return method;
          }
        });

      // Prepare settings update
      const settingsUpdate = {
        businessHours: preferences.openingHours,
        paymentMethods,
        onlineOrdering: preferences.onlineOrdering === 'enabled',
        tableReservations: true, // Default to enabled
        deliveryEnabled: false, // Default to disabled
      };

      // Update branch settings using real API
      await updateBranchSettings({
        shopId: merchantId,
        branchId: branchId,
        settings: settingsUpdate,
      }).unwrap();

      toast.success('Preferences saved successfully!');
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast.error('Failed to save preferences. Please try again.');
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Restaurant Preferences</h1>
          <p className="text-[#8a745c] text-sm">Configure your restaurant settings for {merchant.name} - {branch.name}</p>
        </div>
      </div>

      <div className="max-w-2xl">
        {/* General Settings */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">General</h3>

        <div className="space-y-4 mb-6">
          <div>
            <Label htmlFor="restaurantName" className="text-[#181510] text-base font-medium leading-normal">
              Restaurant Name
            </Label>
            <Input
              id="restaurantName"
              value={preferences.restaurantName}
              onChange={(e) => handleInputChange('restaurantName', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter restaurant name"
            />
          </div>

          <div>
            <Label htmlFor="restaurantAddress" className="text-[#181510] text-base font-medium leading-normal">
              Restaurant Address
            </Label>
            <Input
              id="restaurantAddress"
              value={preferences.restaurantAddress}
              onChange={(e) => handleInputChange('restaurantAddress', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter restaurant address"
            />
          </div>

          <div>
            <Label htmlFor="contactPhone" className="text-[#181510] text-base font-medium leading-normal">
              Contact Phone
            </Label>
            <Input
              id="contactPhone"
              value={preferences.contactPhone}
              onChange={(e) => handleInputChange('contactPhone', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter contact phone"
            />
          </div>

          <div>
            <Label htmlFor="contactEmail" className="text-[#181510] text-base font-medium leading-normal">
              Contact Email
            </Label>
            <Input
              id="contactEmail"
              type="email"
              value={preferences.contactEmail}
              onChange={(e) => handleInputChange('contactEmail', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter contact email"
            />
          </div>
        </div>

        {/* Opening Hours */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Opening Hours</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {Object.entries(preferences.openingHours).map(([day, hours]) => (
            <div key={day}>
              <Label htmlFor={day} className="text-[#181510] text-base font-medium leading-normal capitalize">
                {day}
              </Label>
              <Input
                id={day}
                value={hours}
                onChange={(e) => handleOpeningHoursChange(day as keyof RestaurantPreferences['openingHours'], e.target.value)}
                className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
                placeholder="e.g., 9:00 AM - 10:00 PM"
              />
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Payment Methods</h3>

        <div className="space-y-3 mb-6">
          {Object.entries(preferences.paymentMethods).map(([method, checked]) => (
            <div key={method} className="flex items-center space-x-3">
              <Checkbox
                id={method}
                checked={checked}
                onCheckedChange={(checked) => handlePaymentMethodChange(method as keyof RestaurantPreferences['paymentMethods'], checked as boolean)}
                className="h-5 w-5 border-[#e5e1dc] border-2 data-[state=checked]:bg-[#e58219] data-[state=checked]:border-[#e58219]"
              />
              <Label htmlFor={method} className="text-[#181510] text-base font-normal leading-normal capitalize">
                {method === 'creditCard' ? 'Credit Card' :
                 method === 'debitCard' ? 'Debit Card' :
                 method === 'mobilePayment' ? 'Mobile Payment' :
                 method === 'giftCard' ? 'Gift Card' :
                 method}
              </Label>
            </div>
          ))}
        </div>

        {/* Table Layout */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Table Layout</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <Label htmlFor="numberOfTables" className="text-[#181510] text-base font-medium leading-normal">
              Number of Tables
            </Label>
            <Input
              id="numberOfTables"
              type="number"
              value={preferences.tableLayout.numberOfTables}
              onChange={(e) => handleTableLayoutChange('numberOfTables', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter number of tables"
            />
          </div>

          <div>
            <Label htmlFor="maximumPartySize" className="text-[#181510] text-base font-medium leading-normal">
              Maximum Party Size
            </Label>
            <Input
              id="maximumPartySize"
              type="number"
              value={preferences.tableLayout.maximumPartySize}
              onChange={(e) => handleTableLayoutChange('maximumPartySize', e.target.value)}
              className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 text-base"
              placeholder="Enter maximum party size"
            />
          </div>
        </div>

        {/* Online Ordering */}
        <h3 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4">Online Ordering</h3>

        <RadioGroup
          value={preferences.onlineOrdering}
          onValueChange={(value) => setPreferences(prev => ({ ...prev, onlineOrdering: value as 'enabled' | 'disabled' }))}
          className="space-y-3 mb-6"
        >
          <div className="flex items-center space-x-4 rounded-xl border border-[#e5e1dc] p-4">
            <RadioGroupItem
              value="enabled"
              id="enabled"
              className="h-5 w-5 border-2 border-[#e5e1dc] text-[#181510]"
            />
            <Label htmlFor="enabled" className="text-[#181510] text-sm font-medium leading-normal">
              Enabled
            </Label>
          </div>
          <div className="flex items-center space-x-4 rounded-xl border border-[#e5e1dc] p-4">
            <RadioGroupItem
              value="disabled"
              id="disabled"
              className="h-5 w-5 border-2 border-[#e5e1dc] text-[#181510]"
            />
            <Label htmlFor="disabled" className="text-[#181510] text-sm font-medium leading-normal">
              Disabled
            </Label>
          </div>
        </RadioGroup>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSavePreferences}
            disabled={isUpdating}
            className="bg-[#e58219] hover:bg-[#d67917] text-[#181510] font-bold px-6 h-10"
          >
            {isUpdating ? 'Saving...' : 'Save Preferences'}
          </Button>
        </div>
      </div>
    </div>
  );
}
