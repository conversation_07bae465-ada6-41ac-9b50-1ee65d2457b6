'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, CheckCheck, Search, Trash2, RefreshCw, Filter, SortAsc, SortDesc, Bell, BellRing, AlertCircle } from 'lucide-react';
import NotificationItem from '@/components/notifications/NotificationItem';
import { toast } from 'sonner';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { useNotifications } from '@/hooks/useNotifications';
import type { NotificationsFilters } from '@/lib/redux/api/endpoints/restaurant/notificationsApi';

interface NotificationsPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

export default function NotificationsPage({ params }: NotificationsPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [searchTerm, setSearchTerm] = useState('');

  // Get shop and branch data
  const { branch, shop } = branchWithShop || {};

  // Use the notifications hook with backend API
  const {
    notifications,
    totalCount,
    currentPage,
    totalPages,
    filters,
    stats,
    isLoading,
    isUpdating,
    isMarkingAllAsRead,
    isClearing,
    isError,
    error,
    updateFilters,
    resetFilters,
    handleSearch,
    handleTypeFilter,
    handlePriorityFilter,
    handleReadStatusFilter,
    handleSort,
    handlePageChange,
    handleLimitChange,
    handleMarkAsRead,
    handleMarkAsUnread,
    handleDeleteNotification,
    handleMarkAllAsRead,
    handleClearAllNotifications,
    formatTimestamp,
    getPriorityColor,
    getTypeIcon,
    refetch,
  } = useNotifications({
    shopId: shop?.id || '',
    branchId: branch?.id || '',
    initialFilters: {
      page: 1,
      limit: 20,
      sort_by: 'timestamp',
      sort_order: 'desc'
    }
  });

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    handleSearch(value);
  };

  // Handle mark as read action
  const onMarkAsRead = async (id: string) => {
    try {
      await handleMarkAsRead(id);
      toast.success('Notification marked as read');
    } catch (error) {
      toast.error('Failed to mark notification as read');
    }
  };

  // Handle mark all as read action
  const onMarkAllAsRead = async () => {
    try {
      await handleMarkAllAsRead();
      toast.success('All notifications marked as read');
    } catch (error) {
      toast.error('Failed to mark all notifications as read');
    }
  };

  // Handle clear all notifications action
  const onClearAllNotifications = async () => {
    try {
      await handleClearAllNotifications();
      toast.success('All notifications cleared');
    } catch (error) {
      toast.error('Failed to clear all notifications');
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
    toast.success('Notifications refreshed');
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter notifications based on active tab
  const getFilteredNotifications = (tab: string) => {
    switch (tab) {
      case 'unread':
        return notifications.filter(n => !n.isRead);
      case 'read':
        return notifications.filter(n => n.isRead);
      default:
        return notifications;
    }
  };

  return (
    <div className="font-be-vietnam">
      {/* Header */}
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      {/* Page Title and Controls */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Notifications</h1>
          <p className="text-[#8a745c] text-sm">
            View and manage notifications for {shop?.name} - {branch?.name}
          </p>
        </div>
        <div className="flex items-end gap-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
            <Input
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 w-64 border-[#e2dcd4] focus:border-[#e58219]"
            />
          </div>

          {/* Type Filter */}
          <Select value={filters.type || 'all'} onValueChange={(value) => handleTypeFilter(value === 'all' ? undefined : value as NotificationsFilters['type'])}>
            <SelectTrigger className="w-40 border-[#e2dcd4]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="order">Orders</SelectItem>
              <SelectItem value="reservation">Reservations</SelectItem>
              <SelectItem value="review">Reviews</SelectItem>
              <SelectItem value="system">System</SelectItem>
              <SelectItem value="staff">Staff</SelectItem>
              <SelectItem value="inventory">Inventory</SelectItem>
              <SelectItem value="payment">Payment</SelectItem>
              <SelectItem value="promotion">Promotion</SelectItem>
            </SelectContent>
          </Select>

          {/* Priority Filter */}
          <Select value={filters.priority || 'all'} onValueChange={(value) => handlePriorityFilter(value === 'all' ? undefined : value as NotificationsFilters['priority'])}>
            <SelectTrigger className="w-40 border-[#e2dcd4]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          {/* Sort */}
          <Select value={`${filters.sort_by}-${filters.sort_order}`} onValueChange={(value) => {
            const [sort_by, sort_order] = value.split('-') as [NotificationsFilters['sort_by'], NotificationsFilters['sort_order']];
            handleSort(sort_by, sort_order);
          }}>
            <SelectTrigger className="w-48 border-[#e2dcd4]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="timestamp-desc">Newest First</SelectItem>
              <SelectItem value="timestamp-asc">Oldest First</SelectItem>
              <SelectItem value="priority-desc">High Priority First</SelectItem>
              <SelectItem value="priority-asc">Low Priority First</SelectItem>
              <SelectItem value="type-asc">Type A-Z</SelectItem>
              <SelectItem value="type-desc">Type Z-A</SelectItem>
            </SelectContent>
          </Select>

          {/* Refresh Button */}
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            className="border-[#e2dcd4] text-[#181510]"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg border border-[#e2dcd4]">
          <div className="flex items-center">
            <Bell className="h-5 w-5 text-[#8a745c] mr-2" />
            <div>
              <p className="text-sm text-[#8a745c]">Total</p>
              <p className="text-2xl font-bold text-[#181510]">{stats.totalNotifications}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-[#e2dcd4]">
          <div className="flex items-center">
            <BellRing className="h-5 w-5 text-[#e58219] mr-2" />
            <div>
              <p className="text-sm text-[#8a745c]">Unread</p>
              <p className="text-2xl font-bold text-[#e58219]">{stats.unreadNotifications}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-[#e2dcd4]">
          <div className="flex items-center">
            <CheckCheck className="h-5 w-5 text-green-600 mr-2" />
            <div>
              <p className="text-sm text-[#8a745c]">Read</p>
              <p className="text-2xl font-bold text-green-600">{stats.readNotifications}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-[#e2dcd4]">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <div>
              <p className="text-sm text-[#8a745c]">Urgent</p>
              <p className="text-2xl font-bold text-red-600">{stats.urgentNotifications}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 mb-6">
        <Button
          onClick={onMarkAllAsRead}
          disabled={isMarkingAllAsRead || stats.unreadNotifications === 0}
          className="bg-[#e58219] hover:bg-[#d4751a] text-white"
        >
          <CheckCheck className="h-4 w-4 mr-2" />
          {isMarkingAllAsRead ? 'Marking...' : 'Mark All as Read'}
        </Button>
        <Button
          variant="outline"
          onClick={onClearAllNotifications}
          disabled={isClearing || stats.totalNotifications === 0}
          className="border-red-200 text-red-600 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {isClearing ? 'Clearing...' : 'Clear All'}
        </Button>
      </div>

      {/* Notifications Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-[#f9f7f5] border border-[#e2dcd4]">
          <TabsTrigger value="all" className="data-[state=active]:bg-white data-[state=active]:text-[#181510]">
            All
            <Badge variant="secondary" className="ml-2 bg-[#e2dcd4] text-[#181510]">
              {stats.totalNotifications}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="unread" className="data-[state=active]:bg-white data-[state=active]:text-[#181510]">
            Unread
            <Badge variant="secondary" className="ml-2 bg-[#e58219] text-white">
              {stats.unreadNotifications}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="read" className="data-[state=active]:bg-white data-[state=active]:text-[#181510]">
            Read
            <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
              {stats.readNotifications}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {/* All Notifications */}
        <TabsContent value="all" className="mt-6">
          <div className="bg-white rounded-lg border border-[#e2dcd4] overflow-hidden">
            {notifications.length === 0 ? (
              <div className="text-center py-12">
                <Bell className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
                <h3 className="text-lg font-medium text-[#181510] mb-2">No notifications</h3>
                <p className="text-[#8a745c]">You're all caught up! No notifications to display.</p>
              </div>
            ) : (
              <div className="divide-y divide-[#e2dcd4]">
                {notifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={onMarkAsRead}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        {/* Unread Notifications */}
        <TabsContent value="unread" className="mt-6">
          <div className="bg-white rounded-lg border border-[#e2dcd4] overflow-hidden">
            {getFilteredNotifications('unread').length === 0 ? (
              <div className="text-center py-12">
                <CheckCheck className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-[#181510] mb-2">All caught up!</h3>
                <p className="text-[#8a745c]">You have no unread notifications.</p>
              </div>
            ) : (
              <div className="divide-y divide-[#e2dcd4]">
                {getFilteredNotifications('unread').map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={onMarkAsRead}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        {/* Read Notifications */}
        <TabsContent value="read" className="mt-6">
          <div className="bg-white rounded-lg border border-[#e2dcd4] overflow-hidden">
            {getFilteredNotifications('read').length === 0 ? (
              <div className="text-center py-12">
                <Bell className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
                <h3 className="text-lg font-medium text-[#181510] mb-2">No read notifications</h3>
                <p className="text-[#8a745c]">No notifications have been read yet.</p>
              </div>
            ) : (
              <div className="divide-y divide-[#e2dcd4]">
                {getFilteredNotifications('read').map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={onMarkAsRead}
                  />
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-[#8a745c]">
            Showing {((currentPage - 1) * filters.limit) + 1} to {Math.min(currentPage * filters.limit, totalCount)} of {totalCount} notifications
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="border-[#e2dcd4] text-[#181510]"
            >
              Previous
            </Button>
            <span className="text-sm text-[#8a745c]">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="border-[#e2dcd4] text-[#181510]"
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}