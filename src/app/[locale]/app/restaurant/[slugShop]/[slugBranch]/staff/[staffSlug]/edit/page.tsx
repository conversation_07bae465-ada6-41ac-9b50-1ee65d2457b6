'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';

import { toast } from 'sonner';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useStaffMember, useStaff } from '@/hooks/useStaff';
import React from 'react';

interface StaffEditPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    staffSlug: string;
  }>;
}

export default function StaffEditPage({ params }: StaffEditPageProps) {
  const { slugShop, slugBranch, staffSlug } = React.use(params);
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  // Use staff member hook for data fetching
  const {
    staffMember,
    isLoading: isLoadingStaff,
    isError,
    error,
    updateStaffMember,
  } = useStaffMember(
    slugShop,
    slugBranch,
    undefined, // staffId
    staffSlug   // slug
  );

  // Use staff hook for roles and permissions
  const {
    roles,
    permissions,
  } = useStaff({
    shopSlug: slugShop,
    branchSlug: slugBranch,
  });

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    position: '',
    department: '',
    roleId: '',
    email: '',
    phone: '',
    employeeId: '',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    salary: '',
    hourlyRate: '',
    permissions: [] as string[],
  });

  const isLoading = isLoadingMerchants || isLoadingStaff;

  // Initialize form data when staff member is found
  useEffect(() => {
    if (staffMember) {
      setFormData({
        firstName: staffMember.firstName,
        lastName: staffMember.lastName,
        position: staffMember.position,
        department: staffMember.department,
        roleId: staffMember.roleId,
        email: staffMember.email,
        phone: staffMember.phone,
        employeeId: staffMember.employeeId,
        status: staffMember.status,
        salary: staffMember.salary?.toString() || '',
        hourlyRate: staffMember.hourlyRate?.toString() || '',
        permissions: staffMember.permissions || [],
      });
    }
  }, [staffMember]);

  const handleChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // If role is changed, automatically update permissions based on the selected role
      if (field === 'roleId' && value) {
        const selectedRole = roles?.find(role => role.id === value);
        if (selectedRole) {
          newData.permissions = selectedRole.permissions || [];
        }
      }

      return newData;
    });
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.firstName) {
        toast.error('First name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.lastName) {
        toast.error('Last name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.email) {
        toast.error('Email is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.position) {
        toast.error('Position is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.roleId) {
        toast.error('Role is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.employeeId) {
        toast.error('Employee ID is required');
        setIsSubmitting(false);
        return;
      }

      // Prepare update data
      const updateData = {
        ...formData,
        salary: formData.salary ? parseFloat(formData.salary) : undefined,
        hourlyRate: formData.hourlyRate ? parseFloat(formData.hourlyRate) : undefined,
      };

      // Update staff member using real API
      await updateStaffMember(updateData);

      toast.success('Staff member updated successfully');

      // Redirect back to staff detail page
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`);
    } catch (error) {
      toast.error('Failed to update staff member');
      console.error('Error updating staff member:', error);
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (isError || !staffMember) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Staff
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Staff Member Not Found</h1>
          <p className="text-[#8a745c] text-sm">
            {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
              ? (error.data as { message: string }).message
              : 'The staff member you are looking for does not exist.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Staff Details
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Edit Staff Member</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Update {staffMember.firstName} {staffMember.lastName}'s information at {merchant.name} - {branch.name}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-[512px] space-y-6">
        {/* First Name */}
        <div className="space-y-2">
          <Label htmlFor="firstName" className="text-[#181511] text-base font-medium leading-normal text-left">First Name</Label>
          <Input
            id="firstName"
            placeholder="Enter first name"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.firstName}
            onChange={(e) => handleChange('firstName', e.target.value)}
            required
          />
        </div>

        {/* Last Name */}
        <div className="space-y-2">
          <Label htmlFor="lastName" className="text-[#181511] text-base font-medium leading-normal text-left">Last Name</Label>
          <Input
            id="lastName"
            placeholder="Enter last name"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.lastName}
            onChange={(e) => handleChange('lastName', e.target.value)}
            required
          />
        </div>

        {/* Position */}
        <div className="space-y-2">
          <Label htmlFor="position" className="text-[#181511] text-base font-medium leading-normal text-left">Position</Label>
          <Input
            id="position"
            placeholder="Enter position (e.g., Chef, Server, Manager)"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.position}
            onChange={(e) => handleChange('position', e.target.value)}
            required
          />
        </div>

        {/* Department */}
        <div className="space-y-2">
          <Label htmlFor="department" className="text-[#181511] text-base font-medium leading-normal text-left">Department</Label>
          <Select value={formData.department} onValueChange={(value) => handleChange('department', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Kitchen">Kitchen</SelectItem>
              <SelectItem value="Front of House">Front of House</SelectItem>
              <SelectItem value="Management">Management</SelectItem>
              <SelectItem value="Bar">Bar</SelectItem>
              <SelectItem value="Cleaning">Cleaning</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Role */}
        <div className="space-y-2">
          <Label htmlFor="roleId" className="text-[#181511] text-base font-medium leading-normal text-left">Role</Label>
          <Select value={formData.roleId} onValueChange={(value) => handleChange('roleId', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              {(roles || []).map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Employee ID */}
        <div className="space-y-2">
          <Label htmlFor="employeeId" className="text-[#181511] text-base font-medium leading-normal text-left">Employee ID</Label>
          <Input
            id="employeeId"
            placeholder="Enter employee ID"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.employeeId}
            onChange={(e) => handleChange('employeeId', e.target.value)}
            required
          />
        </div>

        {/* Contact Number */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-[#181511] text-base font-medium leading-normal text-left">Contact Number</Label>
          <Input
            id="phone"
            placeholder="Enter contact number"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
          />
        </div>

        {/* Email Address */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-[#181511] text-base font-medium leading-normal text-left">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter email address"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            required
          />
        </div>

        {/* Status */}
        <div className="space-y-2">
          <Label htmlFor="status" className="text-[#181511] text-base font-medium leading-normal text-left">Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleChange('status', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Salary */}
        <div className="space-y-2">
          <Label htmlFor="salary" className="text-[#181511] text-base font-medium leading-normal text-left">Salary (Optional)</Label>
          <Input
            id="salary"
            type="number"
            placeholder="Enter annual salary"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.salary}
            onChange={(e) => handleChange('salary', e.target.value)}
          />
        </div>

        {/* Hourly Rate */}
        <div className="space-y-2">
          <Label htmlFor="hourlyRate" className="text-[#181511] text-base font-medium leading-normal text-left">Hourly Rate (Optional)</Label>
          <Input
            id="hourlyRate"
            type="number"
            step="0.01"
            placeholder="Enter hourly rate"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.hourlyRate}
            onChange={(e) => handleChange('hourlyRate', e.target.value)}
          />
        </div>

        {/* Role-based Permissions */}
        <div className="space-y-3">
          <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">Permissions</h3>
          <p className="text-[#8a745c] text-sm">
            {formData.roleId
              ? `Permissions are automatically assigned based on the selected role: ${roles?.find(r => r.id === formData.roleId)?.name || 'Selected Role'}`
              : 'Select a role above to see the permissions that will be assigned'
            }
          </p>

          {formData.roleId && formData.permissions.length > 0 && (
            <div className="bg-[#f8f6f3] rounded-lg p-4 border border-[#e2dcd4]">
              <h4 className="text-[#181511] text-sm font-semibold mb-3">
                Assigned Permissions ({formData.permissions.length})
              </h4>
              <div className="flex gap-2 flex-wrap">
                {formData.permissions.map((permissionName, index) => {
                  const permission = permissions?.find(p => p.name === permissionName);
                  return (
                    <div
                      key={permissionName || index}
                      className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full pl-4 pr-4 bg-[#e58219] text-white"
                    >
                      <p className="text-sm font-medium leading-normal">
                        {permission?.name || permissionName}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {!formData.roleId && (
            <div className="bg-[#f8f6f3] rounded-lg p-4 border border-[#e2dcd4] text-center">
              <p className="text-[#8a745c] text-sm">
                Please select a role to see the permissions that will be assigned to this staff member.
              </p>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`}>
            <Button type="button" variant="outline" className="border-[#e2dcd4] text-[#181510]">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] hover:bg-[#d4741a] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
          >
            <span className="truncate">
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </span>
          </Button>
        </div>
      </form>
    </div>
  );
}
