'use client';

import { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useStaffMember } from '@/hooks/useStaff';
import React from 'react';

interface StaffDetailPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    staffSlug: string;
  }>;
}

export default function StaffDetailPage({ params }: StaffDetailPageProps) {
  const { slugShop, slugBranch, staffSlug } = React.use(params);
  const [activeTab, setActiveTab] = useState('overview');

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  // Use staff member hook for data fetching
  const {
    staffMember,
    isLoading: isLoadingStaff,
    isError,
    error,
  } = useStaffMember(
    slugShop,
    slugBranch,
    undefined, // staffId
    staffSlug   // slug
  );

  const isLoading = isLoadingMerchants || isLoadingStaff;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="font-be-vietnam p-6">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (isError || !staffMember) {
    return (
      <div className="font-be-vietnam p-6">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Staff Member Not Found</h1>
          <p className="text-[#81766a] text-sm">
            {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
              ? (error.data as { message: string }).message
              : 'The staff member you are looking for does not exist.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex gap-1 px-6 py-5 font-be-vietnam flex-col">
      {/* Breadcrumb */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`} className="text-[#81766a] text-base font-medium leading-normal">
          Staff
        </Link>
        <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
        <span className="text-[#161412] text-base font-medium leading-normal">Staff Details</span>
      </div>

      <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
        {/* Header with Profile */}
        <div className="flex p-4">
          <div className="flex w-full flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
            <div className="flex gap-4">
              <div
                className="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32"
                style={{ backgroundImage: `url("${staffMember.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1974&auto=format&fit=crop'}")` }}
              ></div>
              <div className="flex flex-col justify-center">
                <p className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em]">{staffMember.firstName} {staffMember.lastName}</p>
                <p className="text-[#81766a] text-base font-normal leading-normal">Employee ID: {staffMember.employeeId}</p>
              </div>
            </div>
            <div className="flex items-end">
              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}/edit`}>
                <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Staff Member
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="pb-3">
          <div className="flex border-b border-[#e3e1dd] px-4 gap-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'overview'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'overview' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Overview</p>
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'schedule'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'schedule' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Schedule</p>
            </button>
            <button
              onClick={() => setActiveTab('performance')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'performance'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'performance' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Performance</p>
            </button>
            <button
              onClick={() => setActiveTab('documents')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'documents'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'documents' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Documents</p>
            </button>
          </div>
        </div>

        {/* Personal Details */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Personal Details</h2>
        <div className="p-4 grid grid-cols-2">
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Full Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.firstName} {staffMember.lastName}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Hire Date</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{new Date(staffMember.hireDate).toLocaleDateString()}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Position</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.position}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Department</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.department}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Address</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              {staffMember.address
                ? `${staffMember.address.street}, ${staffMember.address.city}, ${staffMember.address.state} ${staffMember.address.zipCode}, ${staffMember.address.country}`
                : 'Not provided'
              }
            </p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Status</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              <Badge className={`${
                staffMember.status === 'active'
                  ? 'bg-green-100 text-green-800 hover:bg-green-100'
                  : staffMember.status === 'inactive'
                  ? 'bg-red-100 text-red-800 hover:bg-red-100'
                  : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
              }`}>
                {staffMember.status.charAt(0).toUpperCase() + staffMember.status.slice(1)}
              </Badge>
            </p>
          </div>
        </div>

        {/* Role & Permissions */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Role & Permissions</h2>
        <div className="p-4 grid grid-cols-2">
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Role</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.roleName}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Permissions</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              {staffMember.permissions.length > 0 ? staffMember.permissions.join(', ') : 'No permissions assigned'}
            </p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Salary</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              {staffMember.salary ? `$${staffMember.salary.toLocaleString()}` : 'Not specified'}
            </p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Hourly Rate</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              {staffMember.hourlyRate ? `$${staffMember.hourlyRate}/hr` : 'Not specified'}
            </p>
          </div>
        </div>

        {/* Contact Information */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Contact Information</h2>
        <div className="p-4 grid grid-cols-2">
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Email</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.email}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Phone Number</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.phone}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Emergency Contact Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              {staffMember.emergencyContact?.name || 'Not provided'}
            </p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Emergency Contact Phone</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              {staffMember.emergencyContact?.phone || 'Not provided'}
            </p>
          </div>
        </div>

        {/* Work Schedule */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Work Schedule</h2>
        <div className="px-4 py-3">
          <div className="flex overflow-hidden rounded-xl border border-[#e3e1dd] bg-white">
            <table className="flex-1">
              <thead>
                <tr className="bg-white">
                  <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">Day</th>
                  <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">Start Time</th>
                  <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">End Time</th>
                  <th className="px-4 py-3 text-left text-[#161412] w-60 text-sm font-medium leading-normal">Status</th>
                </tr>
              </thead>
              <tbody>
                {staffMember.schedule && staffMember.schedule.length > 0 ? (
                  staffMember.schedule.map((scheduleItem, index) => {
                    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                    return (
                      <tr key={index} className="border-t border-t-[#e3e1dd]">
                        <td className="h-[72px] px-4 py-2 w-[400px] text-[#161412] text-sm font-normal leading-normal">
                          {dayNames[scheduleItem.dayOfWeek]}
                        </td>
                        <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                          {scheduleItem.isWorkingDay ? scheduleItem.startTime : 'Off'}
                        </td>
                        <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                          {scheduleItem.isWorkingDay ? scheduleItem.endTime : 'Off'}
                        </td>
                        <td className="h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                          <div
                            className={`flex min-w-[84px] max-w-[480px] items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                              scheduleItem.isWorkingDay
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            <span className="truncate">{scheduleItem.isWorkingDay ? 'Working' : 'Off'}</span>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={4} className="h-[72px] px-4 py-2 text-center text-[#81766a] text-sm">
                      No schedule available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Performance Metrics */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Performance Metrics</h2>
        <div className="flex flex-wrap gap-4 p-4">
          {staffMember.performance ? (
            <>
              <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
                <p className="text-[#161412] text-base font-medium leading-normal">Orders Served</p>
                <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">{staffMember.performance.ordersServed || 0}</p>
              </div>
              <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
                <p className="text-[#161412] text-base font-medium leading-normal">Customer Rating</p>
                <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">{staffMember.performance.customerRating?.toFixed(1) || 'N/A'}</p>
              </div>
              <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
                <p className="text-[#161412] text-base font-medium leading-normal">Punctuality Score</p>
                <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">{staffMember.performance.punctualityScore || 0}</p>
              </div>
              <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
                <p className="text-[#161412] text-base font-medium leading-normal">Sales Generated</p>
                <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">${staffMember.performance.salesGenerated?.toLocaleString() || 0}</p>
              </div>
            </>
          ) : (
            <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
              <p className="text-[#161412] text-base font-medium leading-normal">Performance Data</p>
              <p className="text-[#81766a] text-sm">No performance data available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
