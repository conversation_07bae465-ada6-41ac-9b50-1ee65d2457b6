'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { format } from 'date-fns';

// Define order status types
type OrderStatus = 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';

// Define status color mapping
const statusColorMap: Record<OrderStatus, string> = {
  pending: '#f59e0b', // Amber
  preparing: '#3b82f6', // Blue
  ready: '#8b5cf6', // Purple
  completed: '#07880e', // Green
  cancelled: '#ef4444', // Red
};

// Define order item type
interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  options: any[];
  specialInstructions: string;
}

// Define order type
interface Order {
  id: string;
  merchantId: string;
  customerId: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  items: OrderItem[];
  status: OrderStatus;
  total: number;
  subtotal: number;
  tax: number;
  tip: number;
  discount: number;
  paymentMethod: string;
  paymentStatus: string;
  orderType: string;
  tableId?: string;
  createdAt: string;
  updatedAt: string;
  estimatedTime: number;
}

// Mock orders data for development
const mockOrders: Record<string, Order> = {
  '12345': {
    id: '#12345',
    merchantId: 'current-merchant-id',
    customerId: 'customer-1',
    customer: {
      name: 'Olivia Bennett',
      email: '<EMAIL>',
      phone: '(*************',
    },
    items: [
      {
        id: 'item-1',
        menuItemId: 'menu-1',
        name: 'Classic Burger',
        price: 12.00,
        quantity: 2,
        options: [],
        specialInstructions: 'No onions',
      },
      {
        id: 'item-2',
        menuItemId: 'menu-2',
        name: 'Fries',
        price: 4.00,
        quantity: 2,
        options: [],
        specialInstructions: 'Extra salt',
      },
      {
        id: 'item-3',
        menuItemId: 'menu-3',
        name: 'Soda',
        price: 3.00,
        quantity: 2,
        options: [],
        specialInstructions: 'No ice',
      },
    ],
    status: 'completed' as OrderStatus,
    total: 20.52,
    subtotal: 19.00,
    tax: 1.52,
    tip: 0,
    discount: 0,
    paymentMethod: 'credit_card',
    paymentStatus: 'paid',
    orderType: 'dine-in',
    tableId: 'table-5',
    createdAt: '2024-07-26T10:30:00.000Z',
    updatedAt: '2024-07-26T10:30:00.000Z',
    estimatedTime: 15, // in minutes
  },
  '12346': {
    id: '#12346',
    merchantId: 'current-merchant-id',
    customerId: 'customer-2',
    customer: {
      name: 'Noah Thompson',
      email: '<EMAIL>',
      phone: '(*************',
    },
    items: [
      {
        id: 'item-4',
        menuItemId: 'menu-4',
        name: 'Margherita Pizza',
        price: 14.99,
        quantity: 1,
        options: [],
        specialInstructions: 'Extra cheese',
      },
      {
        id: 'item-5',
        menuItemId: 'menu-5',
        name: 'Caesar Salad',
        price: 8.99,
        quantity: 1,
        options: [],
        specialInstructions: 'Dressing on the side',
      },
    ],
    status: 'preparing' as OrderStatus,
    total: 23.98,
    subtotal: 23.98,
    tax: 0,
    tip: 0,
    discount: 0,
    paymentMethod: 'cash',
    paymentStatus: 'paid',
    orderType: 'dine-in',
    tableId: 'table-3',
    createdAt: '2024-07-26T11:15:00.000Z',
    updatedAt: '2024-07-26T11:15:00.000Z',
    estimatedTime: 20, // in minutes
  },
  '12347': {
    id: '#12347',
    merchantId: 'current-merchant-id',
    customerId: 'customer-3',
    customer: {
      name: 'Emma Wilson',
      email: '<EMAIL>',
      phone: '(*************',
    },
    items: [
      {
        id: 'item-6',
        menuItemId: 'menu-6',
        name: 'Chicken Sandwich',
        price: 10.99,
        quantity: 1,
        options: [],
        specialInstructions: 'No mayo',
      },
      {
        id: 'item-7',
        menuItemId: 'menu-7',
        name: 'Sweet Potato Fries',
        price: 4.99,
        quantity: 1,
        options: [],
        specialInstructions: '',
      },
      {
        id: 'item-8',
        menuItemId: 'menu-8',
        name: 'Iced Tea',
        price: 2.99,
        quantity: 1,
        options: [],
        specialInstructions: 'Extra lemon',
      },
    ],
    status: 'pending' as OrderStatus,
    total: 18.97,
    subtotal: 18.97,
    tax: 0,
    tip: 0,
    discount: 0,
    paymentMethod: 'credit_card',
    paymentStatus: 'paid',
    orderType: 'takeout',
    createdAt: '2024-07-26T12:00:00.000Z',
    updatedAt: '2024-07-26T12:00:00.000Z',
    estimatedTime: 15, // in minutes
  },
};

export default function OrderDetailPage() {
  const params = useParams();
  const orderId = params.orderId as string;

  // In a real app, we would get the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // For development, use mock data instead of API call
  const [orderState, setOrderState] = useState<OrderStatus>(
    (mockOrders[orderId as keyof typeof mockOrders]?.status || 'preparing') as OrderStatus
  );

  // Use mock data for development
  const mockOrder = mockOrders[orderId as keyof typeof mockOrders] || mockOrders['12345'];

  // In a real app, we would use the API
  // const { data: order = mockOrder, isLoading, isError } = useGetOrderByIdQuery({
  //   merchantId,
  //   orderId
  // });

  // For development, simulate API response
  const order = { ...mockOrder, status: orderState };
  const isLoading = false;
  const isError = false;
  const isUpdating = false;
  const isCancelling = false;

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMMM dd, yyyy, h:mm a');
  };

  // Handle marking order as completed
  const handleMarkAsCompleted = async () => {
    // In a real app, we would call the API
    // try {
    //   await updateOrderStatus({
    //     merchantId,
    //     orderId,
    //     status: 'completed'
    //   }).unwrap();
    // } catch (error) {
    //   console.error('Failed to update order status:', error);
    // }

    // For development, update local state
    setOrderState('completed');
  };

  // Handle cancelling order
  const handleCancelOrder = async () => {
    // In a real app, we would call the API
    // try {
    //   await cancelOrder({
    //     merchantId,
    //     orderId,
    //     reason: 'Cancelled by staff'
    //   }).unwrap();
    // } catch (error) {
    //   console.error('Failed to cancel order:', error);
    // }

    // For development, update local state
    setOrderState('cancelled');
  };

  if (isLoading) {
    return <div className="p-4">Loading order details...</div>;
  }

  if (isError) {
    return <div className="p-4">Error loading order details. Please try again.</div>;
  }

  return (
    <>
      <div className="flex flex-wrap gap-2 p-4">
        <Link href="/app/restaurant/orders" className="text-[#887663] text-base font-medium leading-normal">
          Orders
        </Link>
        <span className="text-[#887663] text-base font-medium leading-normal">/</span>
        <span className="text-[#181511] text-base font-medium leading-normal">Order {order.id}</span>
      </div>

      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight">Order {order.id}</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">
            Placed on {formatDate(order.createdAt)}
          </p>
        </div>
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Order Summary</h3>
      <div className="px-4 py-3 @container">
        <div className="flex overflow-hidden rounded-xl border border-[#e5e1dc] bg-white">
          <table className="flex-1">
            <thead>
              <tr className="bg-white">
                <th className="table-order-detail-column-120 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Item</th>
                <th className="table-order-detail-column-240 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                  Quantity
                </th>
                <th className="table-order-detail-column-360 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                  Special Requests
                </th>
                <th className="table-order-detail-column-480 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Price</th>
              </tr>
            </thead>
            <tbody>
              {order.items.map((item: OrderItem) => (
                <tr key={item.id} className="border-t border-t-[#e5e1dc]">
                  <td className="table-order-detail-column-120 h-[72px] px-4 py-2 w-[400px] text-[#181511] text-sm font-normal leading-normal">
                    {item.name}
                  </td>
                  <td className="table-order-detail-column-240 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    {item.quantity}
                  </td>
                  <td className="table-order-detail-column-360 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    {item.specialInstructions || '-'}
                  </td>
                  <td className="table-order-detail-column-480 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    ${(item.price * item.quantity).toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <style>
          {`
            @container(max-width:120px){.table-order-detail-column-120{display: none;}}
            @container(max-width:240px){.table-order-detail-column-240{display: none;}}
            @container(max-width:360px){.table-order-detail-column-360{display: none;}}
            @container(max-width:480px){.table-order-detail-column-480{display: none;}}
          `}
        </style>
      </div>

      <div className="p-4">
        <div className="flex justify-between gap-x-6 py-2">
          <p className="text-[#887663] text-sm font-normal leading-normal">Subtotal</p>
          <p className="text-[#181511] text-sm font-normal leading-normal text-right">${order.subtotal.toFixed(2)}</p>
        </div>
        <div className="flex justify-between gap-x-6 py-2">
          <p className="text-[#887663] text-sm font-normal leading-normal">Tax</p>
          <p className="text-[#181511] text-sm font-normal leading-normal text-right">${order.tax.toFixed(2)}</p>
        </div>
        <div className="flex justify-between gap-x-6 py-2">
          <p className="text-[#887663] text-sm font-normal leading-normal">Total</p>
          <p className="text-[#181511] text-sm font-normal leading-normal text-right">${order.total.toFixed(2)}</p>
        </div>
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Customer Details</h3>
      <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
        <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e5e1dc] py-5">
          <p className="text-[#887663] text-sm font-normal leading-normal">Name</p>
          <p className="text-[#181511] text-sm font-normal leading-normal">{order.customer.name}</p>
        </div>
        <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e5e1dc] py-5">
          <p className="text-[#887663] text-sm font-normal leading-normal">Phone</p>
          <p className="text-[#181511] text-sm font-normal leading-normal">{order.customer.phone}</p>
        </div>
        {order.tableId && (
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e5e1dc] py-5">
            <p className="text-[#887663] text-sm font-normal leading-normal">Table</p>
            <p className="text-[#181511] text-sm font-normal leading-normal">Table {order.tableId.replace('table-', '')}</p>
          </div>
        )}
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Order Status</h3>
      <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
        <p className="text-[#181511] text-base font-normal leading-normal flex-1 truncate">Status</p>
        <div className="shrink-0">
          <div className="flex size-7 items-center justify-center">
            <div className="size-3 rounded-full" style={{ backgroundColor: statusColorMap[order.status] }}></div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
        <p className="text-[#181511] text-base font-normal leading-normal flex-1 truncate">Estimated Time</p>
        <div className="shrink-0">
          <p className="text-[#181511] text-base font-normal leading-normal">{order.estimatedTime} minutes</p>
        </div>
      </div>

      <div className="flex justify-stretch">
        <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-end">
          <button
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
            onClick={handleCancelOrder}
            disabled={isCancelling || order.status === 'completed' || order.status === 'cancelled'}
          >
            <span className="truncate">
              {isCancelling ? 'Cancelling...' : 'Cancel Order'}
            </span>
          </button>
          <button
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
            onClick={handleMarkAsCompleted}
            disabled={isUpdating || order.status === 'completed' || order.status === 'cancelled'}
          >
            <span className="truncate">
              {isUpdating ? 'Updating...' : 'Mark as Completed'}
            </span>
          </button>
        </div>
      </div>
    </>
  );
}
