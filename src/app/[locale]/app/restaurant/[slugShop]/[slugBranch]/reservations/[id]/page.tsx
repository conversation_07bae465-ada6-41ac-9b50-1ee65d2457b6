'use client';

import React, { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { Calendar, Clock, ArrowLeft, Trash2, Edit } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useReservation } from '@/hooks/useReservations';
import { toast } from 'sonner';

interface ReservationDetailsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    id: string;
  }>;
}

export default function ReservationDetailsPage({ params }: ReservationDetailsPageProps) {
  const { slugShop, slugBranch, id } = React.use(params);
  const router = useRouter();

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  const merchantId = merchant?.id;

  // Use reservation hook for data fetching and actions
  const {
    reservation,
    isLoading: isLoadingReservation,
    isUpdating,
    isCancelling,
    isError,
    error,
    updateReservation,
    cancelReservation,
  } = useReservation(merchantId || '', id);

  // Action states
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isMarkingNoShow, setIsMarkingNoShow] = useState(false);
  const [isSendingConfirmation, setIsSendingConfirmation] = useState(false);

  const isLoading = isLoadingMerchants || isLoadingReservation;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  if (isError || !reservation) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Reservation Not Found</h1>
        <p className="text-[#81766a] text-sm">
          {error && 'data' in error && typeof error.data === 'object' && error.data && 'message' in error.data
            ? (error.data as { message: string }).message
            : 'The reservation you are looking for does not exist.'}
        </p>
      </div>
    );
  }

  const handleCheckIn = async () => {
    setIsCheckingIn(true);
    try {
      await updateReservation({ status: 'checked_in' });
      toast.success('Customer checked in successfully!');
    } catch (error) {
      toast.error('Failed to check in customer');
    } finally {
      setIsCheckingIn(false);
    }
  };

  const handleMarkNoShow = async () => {
    setIsMarkingNoShow(true);
    try {
      await updateReservation({ status: 'no_show' });
      toast.success('Reservation marked as no-show');
    } catch (error) {
      toast.error('Failed to mark as no-show');
    } finally {
      setIsMarkingNoShow(false);
    }
  };

  const handleSendConfirmation = async () => {
    setIsSendingConfirmation(true);
    try {
      // This would typically call a separate API endpoint for sending confirmations
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Confirmation sent successfully!');
    } catch (error) {
      toast.error('Failed to send confirmation');
    } finally {
      setIsSendingConfirmation(false);
    }
  };

  const handleModifyReservation = () => {
    router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations/${id}/edit`);
  };

  const handleCancelReservation = async () => {
    if (confirm('Are you sure you want to cancel this reservation?')) {
      try {
        await cancelReservation();
        toast.success('Reservation cancelled successfully!');
        router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations`);
      } catch (error) {
        toast.error('Failed to cancel reservation');
      }
    }
  };

  return (
    <div className="gap-1 px-6 flex flex-1 justify-center py-5">
      <div className="layout-content-container flex flex-col max-w-[920px] flex-1">
        {/* Breadcrumb */}
        <div className="flex flex-wrap gap-2 p-4">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations`} className="text-[#81766a] text-base font-medium leading-normal hover:text-[#161412]">
            Reservations
          </Link>
          <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
          <span className="text-[#161412] text-base font-medium leading-normal">Reservation Details</span>
        </div>

        {/* Page Header */}
        <div className="flex flex-wrap justify-between gap-3 p-4">
          <div className="flex min-w-72 flex-col gap-3">
            <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Reservation Details</p>
            <p className="text-[#81766a] text-sm font-normal leading-normal">View and manage reservation details for {reservation.customerName}</p>
          </div>
        </div>

        {/* Customer Information */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Customer Information</h2>
        <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerName}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Email</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerEmail || 'Not provided'}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Phone Number</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerPhone}</p>
          </div>
        </div>

        {/* Reservation Details */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Reservation Details</h2>
        <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Date</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{new Date(reservation.date).toLocaleDateString()}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Time</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.time}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Party Size</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.partySize}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Status</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                reservation.status === 'confirmed'
                  ? 'bg-green-100 text-green-800'
                  : reservation.status === 'pending'
                  ? 'bg-yellow-100 text-yellow-800'
                  : reservation.status === 'cancelled'
                  ? 'bg-red-100 text-red-800'
                  : reservation.status === 'completed'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}
              </span>
            </p>
          </div>
          {reservation.tableId && (
            <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
              <p className="text-[#81766a] text-sm font-normal leading-normal">Table</p>
              <p className="text-[#161412] text-sm font-normal leading-normal">Table {reservation.tableId}</p>
            </div>
          )}
        </div>

        {/* Special Requests */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Special Requests</h2>
        <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">
          {reservation.notes || 'No special requests'}
        </p>

        {/* Action Buttons */}
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-start">
            <button
              onClick={handleModifyReservation}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e5ccb2] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d6bd9e] transition-colors"
            >
              <span className="truncate">Modify Reservation</span>
            </button>
            <button
              onClick={handleCancelReservation}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#e3e1dd] transition-colors"
            >
              <span className="truncate">Cancel Reservation</span>
            </button>
          </div>
        </div>
      </div>

      {/* Sidebar - Reservation Actions */}
      <div className="layout-content-container flex flex-col w-[360px]">
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Reservation Actions</h2>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Check In</p>
          <div className="shrink-0">
            <button
              onClick={handleCheckIn}
              disabled={isCheckingIn}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-fit hover:bg-[#e3e1dd] transition-colors disabled:opacity-50"
            >
              <span className="truncate">{isCheckingIn ? 'Checking...' : 'Check In'}</span>
            </button>
          </div>
        </div>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Mark as No-Show</p>
          <div className="shrink-0">
            <button
              onClick={handleMarkNoShow}
              disabled={isMarkingNoShow}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-fit hover:bg-[#e3e1dd] transition-colors disabled:opacity-50"
            >
              <span className="truncate">{isMarkingNoShow ? 'Marking...' : 'No-Show'}</span>
            </button>
          </div>
        </div>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Send Confirmation</p>
          <div className="shrink-0">
            <button
              onClick={handleSendConfirmation}
              disabled={isSendingConfirmation}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-fit hover:bg-[#e3e1dd] transition-colors disabled:opacity-50"
            >
              <span className="truncate">{isSendingConfirmation ? 'Sending...' : 'Send'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
