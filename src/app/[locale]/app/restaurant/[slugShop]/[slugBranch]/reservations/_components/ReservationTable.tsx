'use client';

import React from 'react';
import { format, parseISO } from 'date-fns';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { ReservationDeleteDialog } from '@/components/ui/delete-confirmation-dialog';
import { useState } from 'react';

interface Reservation {
  id: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  status: string;
  tableId?: string;
  tableName?: string;
  specialRequests?: string;
}

interface ReservationTableProps {
  reservations: Reservation[];
  slugShop: string;
  slugBranch: string;
  onCancelReservation: (id: string) => Promise<void>;
  isLoading?: boolean;
}

export function ReservationTable({
  reservations,
  slugShop,
  slugBranch,
  onCancelReservation,
  isLoading = false
}: ReservationTableProps) {
  const [cancellingId, setCancellingId] = useState<string | null>(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedReservationId, setSelectedReservationId] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Confirmed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>;
      case 'seated':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Seated</Badge>;
      case 'completed':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Completed</Badge>;
      case 'no-show':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">No Show</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };

  const handleCancelClick = (reservationId: string) => {
    setSelectedReservationId(reservationId);
    setShowCancelModal(true);
  };

  const handleConfirmCancel = async () => {
    if (selectedReservationId) {
      setCancellingId(selectedReservationId);
      try {
        await onCancelReservation(selectedReservationId);
      } finally {
        setCancellingId(null);
        setShowCancelModal(false);
        setSelectedReservationId(null);
      }
    }
  };

  if (reservations.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-[#8a745c] text-lg">No reservations found</p>
          <p className="text-[#8a745c] text-sm mt-2">
            Reservations will appear here once they are created.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-[#e2dcd4] hover:bg-[#e2dcd4]">
                <TableHead className="text-[#181510] font-medium">Customer</TableHead>
                <TableHead className="text-[#181510] font-medium">Date & Time</TableHead>
                <TableHead className="text-[#181510] font-medium">Party Size</TableHead>
                <TableHead className="text-[#181510] font-medium">Table</TableHead>
                <TableHead className="text-[#181510] font-medium">Status</TableHead>
                <TableHead className="text-[#181510] font-medium">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reservations.map((reservation) => (
                <TableRow key={reservation.id} className="hover:bg-[#f9f7f4]">
                  <TableCell>
                    <div>
                      <div className="font-medium text-[#181510]">{reservation.customerName}</div>
                      <div className="text-sm text-[#8a745c]">
                        {reservation.customerEmail || reservation.customerPhone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-[#181510]">
                        {reservation.date ? format(parseISO(reservation.date), 'MMM d, yyyy') : 'No date'}
                      </div>
                      <div className="text-sm text-[#8a745c]">{reservation.time || 'No time'}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-[#181510]">
                    {reservation.partySize} {reservation.partySize === 1 ? 'person' : 'people'}
                  </TableCell>
                  <TableCell className="text-[#181510]">
                    {reservation.tableName || reservation.tableId || 'Not assigned'}
                  </TableCell>
                  <TableCell>{getStatusBadge(reservation.status)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/${reservation.id}`}>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-8 px-3 border-[#e2dcd4] text-[#181510] hover:bg-[#f1edea]"
                        >
                          {reservation.status === 'completed' || reservation.status === 'cancelled' ? 'View' : 'Edit'}
                        </Button>
                      </Link>
                      {reservation.status !== 'cancelled' &&
                       reservation.status !== 'completed' &&
                       reservation.status !== 'no-show' && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-8 px-3 border-[#e2dcd4] hover:bg-red-50 hover:text-red-600 hover:border-red-200"
                          onClick={() => handleCancelClick(reservation.id)}
                          disabled={cancellingId === reservation.id || isLoading}
                        >
                          {cancellingId === reservation.id ? 'Cancelling...' : 'Cancel'}
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <ReservationDeleteDialog
        open={showCancelModal}
        onOpenChange={setShowCancelModal}
        onConfirm={handleConfirmCancel}
        isLoading={cancellingId !== null}
        reservationId={selectedReservationId || undefined}
      />
    </>
  );
}
