'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Filter, X } from 'lucide-react';

interface ReservationFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  statusFilter: string | null;
  onStatusFilterChange: (value: string | null) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
}

export function ReservationFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  onClearFilters,
  hasActiveFilters
}: ReservationFiltersProps) {
  return (
    <Card className="bg-[#f1edea] border-[#e2dcd4]">
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Search Input */}
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <Input
                placeholder="Search by name, email, or phone"
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 bg-[#fbfaf9] border-[#e2dcd4] text-[#181510] placeholder:text-[#8a745c]"
              />
            </div>

            {/* Status Filter */}
            <div className="flex items-center gap-2">
              <Filter className="text-[#8a745c] h-4 w-4" />
              <Select 
                value={statusFilter || 'all'} 
                onValueChange={(value) => onStatusFilterChange(value === 'all' ? null : value)}
              >
                <SelectTrigger className="w-[180px] bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="seated">Seated</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no-show">No Show</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              className="border-[#e2dcd4] text-[#8a745c] hover:bg-[#e2dcd4] hover:text-[#181510]"
            >
              <X className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="mt-3 pt-3 border-t border-[#e2dcd4]">
            <div className="flex flex-wrap gap-2 items-center">
              <span className="text-sm text-[#8a745c]">Active filters:</span>
              {searchTerm && (
                <div className="inline-flex items-center gap-1 px-2 py-1 bg-[#e5ccb2] text-[#181510] rounded-md text-xs">
                  Search: "{searchTerm}"
                  <button
                    onClick={() => onSearchChange('')}
                    className="ml-1 hover:text-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}
              {statusFilter && (
                <div className="inline-flex items-center gap-1 px-2 py-1 bg-[#e5ccb2] text-[#181510] rounded-md text-xs">
                  Status: {statusFilter}
                  <button
                    onClick={() => onStatusFilterChange(null)}
                    className="ml-1 hover:text-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
