'use client';

import React from 'react';
import { format, parseISO } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, Clock, Users } from 'lucide-react';

interface Reservation {
  id: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  status: string;
  tableId?: string;
  tableName?: string;
  specialRequests?: string;
}

interface ReservationCalendarProps {
  selectedDate: Date | undefined;
  onDateSelect: (date: Date | undefined) => void;
  reservations: Reservation[];
}

export function ReservationCalendar({
  selectedDate,
  onDateSelect,
  reservations
}: ReservationCalendarProps) {
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 text-xs">Confirmed</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200 text-xs">Pending</Badge>;
      case 'cancelled':
        return <Badge variant="secondary" className="bg-red-100 text-red-800 border-red-200 text-xs">Cancelled</Badge>;
      case 'seated':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200 text-xs">Seated</Badge>;
      case 'completed':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800 border-purple-200 text-xs">Completed</Badge>;
      case 'no-show':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200 text-xs">No Show</Badge>;
      default:
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200 text-xs">{status}</Badge>;
    }
  };

  // Filter reservations for the selected date
  const selectedDateReservations = selectedDate
    ? reservations.filter(reservation => 
        reservation.date === format(selectedDate, 'yyyy-MM-dd')
      )
    : [];

  // Sort reservations by time
  const sortedReservations = selectedDateReservations.sort((a, b) => 
    a.time.localeCompare(b.time)
  );

  return (
    <Card className="bg-[#f1edea] border-[#e2dcd4]">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-medium text-[#181510] flex items-center gap-2">
          <CalendarDays className="h-5 w-5" />
          Reservation Calendar
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Calendar Component */}
        <div className="bg-[#fbfaf9] rounded-lg p-3 border border-[#e2dcd4]">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={onDateSelect}
            className="w-full"
            classNames={{
              months: "flex w-full flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
              month: "space-y-4 w-full flex flex-col",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium text-[#181510]",
              nav: "space-x-1 flex items-center",
              nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 text-[#181510]",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell: "text-[#8a745c] rounded-md w-9 font-normal text-[0.8rem]",
              row: "flex w-full mt-2",
              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-[#e5ccb2] first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 text-[#181510] hover:bg-[#e2dcd4] rounded-md",
              day_selected: "bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] hover:text-[#181510] focus:bg-[#e5ccb2] focus:text-[#181510]",
              day_today: "bg-[#8a745c] text-white",
              day_outside: "text-[#8a745c] opacity-50",
              day_disabled: "text-[#8a745c] opacity-50",
              day_range_middle: "aria-selected:bg-[#e5ccb2] aria-selected:text-[#181510]",
              day_hidden: "invisible",
            }}
          />
        </div>

        {/* Selected Date Reservations */}
        <div>
          <h3 className="text-md font-medium mb-3 text-[#181510] flex items-center gap-2">
            <Clock className="h-4 w-4" />
            {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}
          </h3>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {sortedReservations.length > 0 ? (
              sortedReservations.map((reservation) => (
                <div 
                  key={reservation.id} 
                  className="bg-[#fbfaf9] p-3 rounded-md border border-[#e2dcd4] hover:bg-white transition-colors"
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3 text-[#8a745c]" />
                      <span className="font-medium text-[#181510]">{reservation.time}</span>
                    </div>
                    {getStatusBadge(reservation.status)}
                  </div>
                  
                  <div className="space-y-1">
                    <div className="font-medium text-[#181510]">{reservation.customerName}</div>
                    <div className="flex items-center gap-4 text-sm text-[#8a745c]">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {reservation.partySize} {reservation.partySize === 1 ? 'person' : 'people'}
                      </div>
                      {reservation.tableName && (
                        <div>Table: {reservation.tableName}</div>
                      )}
                    </div>
                    {reservation.specialRequests && (
                      <div className="text-xs text-[#8a745c] mt-1 italic">
                        Note: {reservation.specialRequests}
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-[#8a745c] text-center py-6 bg-[#fbfaf9] rounded-md border border-[#e2dcd4]">
                <CalendarDays className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No reservations for this date</p>
                <p className="text-xs mt-1">Select a different date to view reservations</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
