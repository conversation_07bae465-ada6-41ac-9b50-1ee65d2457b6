'use client';

import { useState } from 'react';
import { Link } from '@/i18n/navigation';
import { format, parseISO, isToday, isFuture} from 'date-fns';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useReservations } from '@/hooks/useReservations';
import { useGetShopBySlugQuery, type Shop, type ShopBranch } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetTablesQuery } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import React from 'react';

// Import new components
import { ReservationTable } from './_components/ReservationTable';
import { ReservationFilters } from './_components/ReservationFilters';
import { ReservationCalendar } from './_components/ReservationCalendar';
import { ReservationForm } from './_components/ReservationForm';

interface ReservationsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function ReservationsPage({ params }: ReservationsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // State for UI
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isAddReservationOpen, setIsAddReservationOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('upcoming');

  // Get shop data from backend by slug (includes branches)
  const { data: shop, isLoading: isLoadingShop } = useGetShopBySlugQuery(slugShop);

  // Find the branch from the shop data
  const branch = shop?.branches?.find((b: ShopBranch) => b.slug === slugBranch);

  const branchId = branch?.id;

  // Get tables data from backend
  const {
    data: tablesData,
    isLoading: isLoadingTables
  } = useGetTablesQuery(
    shop?.id && branch?.id ? {
      shopId: shop.id,
      branchId: branch.id
    } : { shopId: '', branchId: '' },
    {
      skip: !shop?.id || !branch?.id
    }
  );

  // Get reservations data from backend
  const {
    reservations,
    isLoading: isLoadingReservations,
    refetch: refetchReservations,
    createReservation,
    cancelReservation,
  } = useReservations({
    shopId: shop?.id || '',
    branchId: branchId || '',
    initialFilters: {
      status: statusFilter || undefined,
      date: selectedDate ? format(selectedDate, 'yyyy-MM-dd') : undefined,
      search: searchTerm || undefined,
    }
  });

  const isLoading = isLoadingShop || isLoadingTables || isLoadingReservations;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!shop || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Use real tables data or fallback to empty array
  const tables = tablesData || [];

  // Filter reservations
  const filteredReservations = reservations.filter(reservation => {
    // Filter by search term (customer name or email)
    const matchesSearch = searchTerm === '' ||
      reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (reservation.customerEmail && reservation.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()));

    // Filter by status
    const matchesStatus = statusFilter === null || reservation.status === statusFilter;

    // Filter by date
    const matchesDate = !selectedDate || (reservation.date && reservation.date === format(selectedDate, 'yyyy-MM-dd'));

    // Filter by tab (upcoming or past)
    if (!reservation.date || !reservation.time) {
      return false; // Skip reservations without date/time
    }

    const isUpcoming = isFuture(parseISO(reservation.date)) ||
      (isToday(parseISO(reservation.date)) && reservation.time >= format(new Date(), 'HH:mm'));
    const matchesTab = (activeTab === 'upcoming' && isUpcoming) || (activeTab === 'past' && !isUpcoming);

    return matchesSearch && matchesStatus && matchesDate && matchesTab;
  });

  // Handle reservation creation success
  const handleCreateSuccess = async (reservationData: {
    customerName: string;
    contactInformation: string;
    date: string;
    time: string;
    tableId?: string;
    partySize: number;
  }) => {
    try {
      // Transform the data to match the frontend API expectations
      const apiData = {
        customerName: reservationData.customerName,
        customerPhone: reservationData.contactInformation, // Use contact info as phone
        customerEmail: '', // Optional field
        date: reservationData.date,
        time: reservationData.time,
        partySize: reservationData.partySize,
        tableId: reservationData.tableId === 'none' ? undefined : reservationData.tableId,
        specialRequests: '', // Not included in simplified form
        duration: 120, // Default 2 hours
        source: 'website', // Set source
      };

      await createReservation(apiData);
      setIsAddReservationOpen(false);
      toast.success('Reservation created successfully');
      refetchReservations();
    } catch {
      toast.error('Failed to create reservation');
    }
  };

  // Handle reservation cancellation
  const handleCancelReservation = async (id: string) => {
    try {
      await cancelReservation(id);
      toast.success('Reservation cancelled');
      refetchReservations();
    } catch {
      toast.error('Failed to cancel reservation');
    }
  };

  // Check if filters are active
  const hasActiveFilters = searchTerm !== '' || statusFilter !== null;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#181510]">Reservations</h1>
          <p className="text-[#8a745c]">Manage reservations for {shop.name} - {branch.name}</p>
        </div>
        <Dialog open={isAddReservationOpen} onOpenChange={setIsAddReservationOpen}>
          <DialogTrigger asChild>
            <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
              New Reservation
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Create New Reservation</DialogTitle>
            </DialogHeader>
            <ReservationForm
              tables={tables.map(table => ({ id: table.id, number: parseInt(table.number) }))}
              onSuccess={handleCreateSuccess}
              onCancel={() => setIsAddReservationOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          {/* Filters */}
          <ReservationFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            onClearFilters={() => {
              setSearchTerm('');
              setStatusFilter(null);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Tabs for Upcoming/Past Reservations */}
          <Tabs defaultValue="upcoming" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4 bg-[#e2dcd4]">
              <TabsTrigger value="upcoming" className="data-[state=active]:bg-[#fbfaf9]">Upcoming</TabsTrigger>
              <TabsTrigger value="past" className="data-[state=active]:bg-[#fbfaf9]">Past</TabsTrigger>
            </TabsList>

            <TabsContent value="upcoming" className="mt-0">
              <ReservationTable
                reservations={filteredReservations.filter(reservation => {
                  if (!reservation.date || !reservation.time) return false;
                  const isUpcoming = isFuture(parseISO(reservation.date)) ||
                    (isToday(parseISO(reservation.date)) && reservation.time >= format(new Date(), 'HH:mm'));
                  return isUpcoming;
                })}
                slugShop={slugShop}
                slugBranch={slugBranch}
                onCancelReservation={handleCancelReservation}
                isLoading={isLoadingReservations}
              />
            </TabsContent>

            <TabsContent value="past" className="mt-0">
              <ReservationTable
                reservations={filteredReservations.filter(reservation => {
                  if (!reservation.date || !reservation.time) return false;
                  const isUpcoming = isFuture(parseISO(reservation.date)) ||
                    (isToday(parseISO(reservation.date)) && reservation.time >= format(new Date(), 'HH:mm'));
                  return !isUpcoming;
                })}
                slugShop={slugShop}
                slugBranch={slugBranch}
                onCancelReservation={handleCancelReservation}
                isLoading={isLoadingReservations}
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Calendar Sidebar */}
        <div className="md:col-span-1">
          <ReservationCalendar
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            reservations={filteredReservations}
          />
        </div>
      </div>
    </div>
  );
}