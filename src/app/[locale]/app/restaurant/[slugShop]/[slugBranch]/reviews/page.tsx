'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Star, ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useReviews } from '@/hooks/useReviews';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import React from 'react';


interface ReviewsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function ReviewsPage({ params }: ReviewsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // State for UI
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [isRespondDialogOpen, setIsRespondDialogOpen] = useState(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [responseText, setResponseText] = useState('');

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  const merchantId = merchant?.id;
  const branchId = branch?.id;

  // Get reviews data from backend
  const {
    reviews,
    reviewStats,
    isLoading: isLoadingReviews,
    refetch: refetchReviews,
    respondToReview,
    updateStatus,
    updateFilters,
  } = useReviews({
    merchantId: merchantId || '',
    branchId: branchId || '',
    initialFilters: {
      status: activeTab === 'all' ? undefined : activeTab,
      rating: ratingFilter ? parseInt(ratingFilter) : undefined,
      search: searchTerm || undefined,
    }
  });

  const isLoading = isLoadingMerchants || isLoadingReviews;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!merchant || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Update filters when UI state changes
  React.useEffect(() => {
    updateFilters({
      status: activeTab === 'all' ? undefined : activeTab,
      rating: ratingFilter ? parseInt(ratingFilter) : undefined,
      search: searchTerm || undefined,
    });
  }, [activeTab, ratingFilter, searchTerm, updateFilters]);

  // Use real reviews data from backend
  const filteredReviews = reviews;

  // Handle responding to a review
  const handleOpenRespondDialog = (reviewId: string) => {
    const review = reviews.find(r => r.id === reviewId);
    setSelectedReviewId(reviewId);
    setResponseText(review?.response?.message || '');
    setIsRespondDialogOpen(true);
  };

  // Handle submitting a response
  const handleSubmitResponse = async () => {
    if (!selectedReviewId) return;

    try {
      await respondToReview(selectedReviewId, responseText);
      setIsRespondDialogOpen(false);
      toast.success('Response submitted successfully');
      refetchReviews();
    } catch (error) {
      toast.error('Failed to submit response');
    }
  };

  // Handle updating review status
  const handleUpdateStatus = async (reviewId: string, status: string) => {
    try {
      await updateStatus(reviewId, status);
      toast.success(`Review ${status} successfully`);
      refetchReviews();
    } catch (error) {
      toast.error(`Failed to update review status`);
    }
  };

  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  // Use real merchant and branch data

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Reviews</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage and respond to customer reviews for {merchant.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-end gap-3">
          <div className="flex flex-col gap-1.5">
            <label htmlFor="rating-filter" className="text-xs font-medium text-[#8a745c]">
              Filter by Rating
            </label>
            <Select value={ratingFilter || 'all'} onValueChange={setRatingFilter}>
              <SelectTrigger id="rating-filter" className="w-[120px] bg-[#fbfaf9] border-[#e5e1dc]">
                <SelectValue placeholder="All Ratings" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-1.5">
            <label htmlFor="search-reviews" className="text-xs font-medium text-[#8a745c]">
              Search Reviews
            </label>
            <Input
              id="search-reviews"
              placeholder="Search by name or content"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[250px] bg-[#fbfaf9] border-[#e5e1dc]"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="bg-[#f1edea] p-1">
          <TabsTrigger value="all" className="data-[state=active]:bg-white">All Reviews</TabsTrigger>
          <TabsTrigger value="published" className="data-[state=active]:bg-white">Published</TabsTrigger>
          <TabsTrigger value="pending" className="data-[state=active]:bg-white">Pending</TabsTrigger>
          <TabsTrigger value="flagged" className="data-[state=active]:bg-white">Flagged</TabsTrigger>
        </TabsList>

        {/* Reviews List */}
        {filteredReviews.length === 0 ? (
          <div className="text-center py-10 text-[#8a745c]">
            No reviews found. Adjust your filters to see more reviews.
          </div>
        ) : (
          <div className="space-y-4">
            {filteredReviews.map((review) => (
              <div key={review.id} className="bg-[#fbfaf9] rounded-lg border border-[#e5e1dc] p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                      <img
                        src={review.reviewer.avatar}
                        alt={review.reviewer.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://via.placeholder.com/40?text=User';
                        }}
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#181510]">{review.customerName}</h3>
                      <div className="flex items-center mt-1">
                        <div className="flex mr-2">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-xs text-[#8a745c]">
                          {format(new Date(review.createdAt), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      review.status === 'published'
                        ? 'bg-green-100 text-green-800'
                        : review.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                    }`}>
                      {review.status.charAt(0).toUpperCase() + review.status.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-[#181510]">{review.comment}</p>
                </div>

                {review.title && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-[#181510] mb-2">Review Title:</h4>
                    <p className="text-[#8a745c] text-sm">{review.title}</p>
                  </div>
                )}

                {review.response && (
                  <div className="bg-[#f1edea] rounded-md p-4 mb-4">
                    <h4 className="text-sm font-medium text-[#181510] mb-2">Your Response:</h4>
                    <p className="text-[#8a745c] text-sm">{review.response.message}</p>
                    <p className="text-[#8a745c] text-xs mt-2">
                      Responded on {format(new Date(review.response.respondedAt), 'MMM d, yyyy')}
                    </p>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-[#e2dcd4]"
                    onClick={() => handleOpenRespondDialog(review.id)}
                  >
                    {review.response ? 'Edit Response' : 'Respond'}
                  </Button>

                  {review.status !== 'approved' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-[#e2dcd4] text-green-600 hover:bg-green-50 hover:text-green-700"
                      onClick={() => handleUpdateStatus(review.id, 'approved')}
                    >
                      Approve
                    </Button>
                  )}

                  {review.status !== 'flagged' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-[#e2dcd4] text-red-600 hover:bg-red-50 hover:text-red-700"
                      onClick={() => handleUpdateStatus(review.id, 'flagged')}
                    >
                      Flag
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Tabs>

      {/* Response Dialog */}
      <Dialog open={isRespondDialogOpen} onOpenChange={setIsRespondDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Respond to Review</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Type your response here..."
              value={responseText}
              onChange={(e) => setResponseText(e.target.value)}
              rows={6}
              className="resize-none"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRespondDialogOpen(false)}
              className="border-[#e2dcd4]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitResponse}
              className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
            >
              Submit Response
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
