'use client';

import React from 'react';
import { Search } from 'lucide-react';

interface MenuSearchProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export function MenuSearch({ searchTerm, onSearchChange }: MenuSearchProps) {
  return (
    <div className="px-4 py-3">
      <label className="flex flex-col min-w-40 h-12 w-full">
        <div className="flex w-full flex-1 items-stretch rounded-xl h-full">
          <div className="text-[#81766a] flex border-none bg-[#f4f2f1] items-center justify-center pl-4 rounded-l-xl border-r-0">
            <Search size={24} />
          </div>
          <input
            placeholder="Search menu items"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-full placeholder:text-[#81766a] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
      </label>
    </div>
  );
}
