'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { FallbackBackgroundImage } from '@/components/ui/fallback-image';

interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  category: string;
  price: number;
  image?: string;
  available: boolean;
}

interface MenuItemCardProps {
  item: MenuItem;
  slugShop: string;
  slugBranch: string;
}

export function MenuItemCard({ item, slugShop, slugBranch }: MenuItemCardProps) {
  return (
    <Link key={item.id} href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}`}>
      <div className="flex flex-col gap-3 pb-3 cursor-pointer hover:opacity-80 transition-opacity">
        <FallbackBackgroundImage
          src={item.image}
          className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
          fallbackSrc="https://via.placeholder.com/400x400/f4f2f1/161412?text=No+Image"
        />
        <div>
          <p className="text-[#161412] text-base font-medium leading-normal">{item.name}</p>
          <p className="text-[#81766a] text-sm font-normal leading-normal">{item.category} - ${item.price.toFixed(2)}</p>
        </div>
      </div>
    </Link>
  );
}
