'use client';

import React from 'react';
import { MenuItemCard } from './MenuItemCard';

interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  category: string;
  price: number;
  image?: string;
  available: boolean;
}

interface MenuGridViewProps {
  items: MenuItem[];
  slugShop: string;
  slugBranch: string;
  searchTerm: string;
}

export function MenuGridView({ items, slugShop, slugBranch, searchTerm }: MenuGridViewProps) {
  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
      {items.length === 0 ? (
        <div className="col-span-full flex items-center justify-center h-64 text-[#81766a]">
          {searchTerm ? 'No menu items match your search' : 'No menu items found'}
        </div>
      ) : (
        items.map((item) => (
          <MenuItemCard
            key={item.id}
            item={item}
            slugShop={slugShop}
            slugBranch={slugBranch}
          />
        ))
      )}
    </div>
  );
}
