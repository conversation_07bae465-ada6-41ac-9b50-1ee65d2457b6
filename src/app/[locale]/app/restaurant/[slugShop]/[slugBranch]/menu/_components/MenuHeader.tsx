'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { Sparkles, Plus } from 'lucide-react';

interface MenuHeaderProps {
  slugShop: string;
  slugBranch: string;
}

export function MenuHeader({ slugShop, slugBranch }: MenuHeaderProps) {
  return (
    <div className="flex flex-wrap justify-between gap-3 p-4">
      <div className="flex min-w-72 flex-col gap-3">
        <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Menu Management</p>
        <p className="text-[#81766a] text-sm font-normal leading-normal">
          Manage your restaurant's menu items, including descriptions, ingredients, pricing, and availability.
        </p>
      </div>
      <div className="flex items-start gap-3">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/add`}>
          <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#161412] text-white text-sm font-bold leading-normal tracking-[0.015em] gap-2 hover:bg-[#2a2520] transition-colors">
            <Plus size={16} />
            <span className="truncate">Add Menu Item</span>
          </button>
        </Link>
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate`}>
          <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-white text-sm font-bold leading-normal tracking-[0.015em] gap-2 hover:bg-[#d4741a] transition-colors">
            <Sparkles size={16} />
            <span className="truncate">AI Generate</span>
          </button>
        </Link>
      </div>
    </div>
  );
}
