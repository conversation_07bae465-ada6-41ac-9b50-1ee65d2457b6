'use client';

import React from 'react';

interface MenuViewToggleProps {
  viewMode: 'table' | 'grid';
  onViewModeChange: (mode: 'table' | 'grid') => void;
}

export function MenuViewToggle({ viewMode, onViewModeChange }: MenuViewToggleProps) {
  return (
    <div className="pb-3">
      <div className="flex border-b border-[#e3e1dd] px-4 gap-8">
        <button
          onClick={() => onViewModeChange('table')}
          className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
            viewMode === 'table'
              ? 'border-b-[#161412] text-[#161412]'
              : 'border-b-transparent text-[#81766a]'
          }`}
        >
          <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
            viewMode === 'table' ? 'text-[#161412]' : 'text-[#81766a]'
          }`}>Table View</p>
        </button>
        <button
          onClick={() => onViewModeChange('grid')}
          className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
            viewMode === 'grid'
              ? 'border-b-[#161412] text-[#161412]'
              : 'border-b-transparent text-[#81766a]'
          }`}
        >
          <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
            viewMode === 'grid' ? 'text-[#161412]' : 'text-[#81766a]'
          }`}>Grid View</p>
        </button>
      </div>
    </div>
  );
}
