'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  Package,
  Plus,
  Search,
  Filter,
  AlertTriangle,
  TrendingDown,
  Calendar,
  DollarSign,
  BarChart3,
  RefreshCw,
  Download,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  ShoppingCart,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { useInventory } from '@/hooks/useInventory';
import type { InventoryFilters } from '@/lib/redux/api/endpoints/restaurant/inventoryApi';

interface InventoryPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

export default function InventoryPage({ params }: InventoryPageProps) {
  const t = useTranslations('inventory');
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [searchTerm, setSearchTerm] = useState('');

  // Get shop and branch data
  const { branch, shop } = branchWithShop || {};

  // Use the inventory hook with backend API
  const {
    items,
    totalCount,
    currentPage,
    totalPages,
    filters,
    stats,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isError,
    error,
    updateFilters,
    resetFilters,
    handleSearch,
    handleCategoryFilter,
    handleStatusFilter,
    handleSupplierFilter,
    handleStockLevelFilter,
    handleSort,
    handlePageChange,
    handleLimitChange,
    handleCreateItem,
    handleUpdateItem,
    handleDeleteItem,
    handleStockAdjustment,
    getItemById,
    getLowStockItems,
    getOutOfStockItems,
    getExpiringItems,
    formatCurrency,
    formatDate,
    getStatusColor,
    isExpiringSoon,
    refetch,
  } = useInventory({
    shopId: shop?.id || '',
    branchId: branch?.id || '',
    initialFilters: {
      page: 1,
      limit: 20,
      sort_by: 'name',
      sort_order: 'asc'
    }
  });

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    handleSearch(value);
  };

  // Handle delete action
  const onDeleteItem = async (id: string) => {
    try {
      await handleDeleteItem(id);
      toast.success('Inventory item deleted successfully');
    } catch (error) {
      toast.error('Failed to delete inventory item');
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
    toast.success('Inventory refreshed');
  };

  // Status badge styling
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'in-stock': { color: 'bg-green-100 text-green-800', icon: Package },
      'low-stock': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
      'out-of-stock': { color: 'bg-red-100 text-red-800', icon: TrendingDown },
      'expired': { color: 'bg-gray-100 text-gray-800', icon: Calendar },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['in-stock'];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <Package className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Get unique categories and suppliers for filters
  const categories = Array.from(new Set(items.map(item => item.category)));
  const suppliers = Array.from(new Set(items.map(item => item.supplier)));

  return (
    <div className="font-be-vietnam space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Package className="w-8 h-8 text-[#e58219]" />
          <div>
            <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Inventory Management</h1>
            <p className="text-[#8a745c] text-sm">
              Track and manage inventory for {shop?.name} - {branch?.name}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            className="border-[#e2dcd4] text-[#181510]"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/add`}>
            <Button className="bg-[#e58219] hover:bg-[#d4751a] text-white">
              <Plus className="w-4 h-4 mr-2" />
              Add Item
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="border-[#e2dcd4]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Total Items</CardTitle>
            <Package className="h-4 w-4 text-[#8a745c]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#181510]">{stats.totalItems}</div>
            <p className="text-xs text-[#8a745c]">
              Active inventory items
            </p>
          </CardContent>
        </Card>

        <Card className="border-[#e2dcd4]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Low Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.lowStockItems}</div>
            <p className="text-xs text-[#8a745c]">
              Items below minimum ({stats.lowStockPercentage}%)
            </p>
          </CardContent>
        </Card>

        <Card className="border-[#e2dcd4]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.outOfStockItems}</div>
            <p className="text-xs text-[#8a745c]">
              Items need restocking ({stats.outOfStockPercentage}%)
            </p>
          </CardContent>
        </Card>

        <Card className="border-[#e2dcd4]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-[#8a745c]">
              Current inventory value
            </p>
          </CardContent>
        </Card>

        <Card className="border-[#e2dcd4]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Expiring Soon</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.expiringSoonItems}</div>
            <p className="text-xs text-[#8a745c]">
              Items expire in 3 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer border-[#e2dcd4]" onClick={() => handleStockLevelFilter('lowStock', true)}>
          <CardContent className="flex items-center p-6">
            <AlertTriangle className="h-8 w-8 text-yellow-600 mr-4" />
            <div>
              <h3 className="font-semibold text-[#181510]">Low Stock Alert</h3>
              <p className="text-sm text-[#8a745c]">{stats.lowStockItems} items need attention</p>
            </div>
          </CardContent>
        </Card>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/purchase-orders`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer border-[#e2dcd4]">
            <CardContent className="flex items-center p-6">
              <ShoppingCart className="h-8 w-8 text-[#e58219] mr-4" />
              <div>
                <h3 className="font-semibold text-[#181510]">Purchase Orders</h3>
                <p className="text-sm text-[#8a745c]">Manage supplier orders</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Card className="hover:shadow-md transition-shadow cursor-pointer border-[#e2dcd4]" onClick={() => handleStockLevelFilter('expiringSoon', true)}>
          <CardContent className="flex items-center p-6">
            <Calendar className="h-8 w-8 text-orange-600 mr-4" />
            <div>
              <h3 className="font-semibold text-[#181510]">Expiring Soon</h3>
              <p className="text-sm text-[#8a745c]">{stats.expiringSoonItems} items expiring</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-wrap gap-4 items-center">
        {/* Search */}
        <div className="flex-1 min-w-64">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] w-4 h-4" />
            <Input
              placeholder="Search items, SKU, or supplier..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 border-[#e2dcd4] focus:border-[#e58219]"
            />
          </div>
        </div>

        {/* Category Filter */}
        <Select value={filters.category || 'all'} onValueChange={(value) => handleCategoryFilter(value === 'all' ? undefined : value)}>
          <SelectTrigger className="w-48 border-[#e2dcd4]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Status Filter */}
        <Select value={filters.status || 'all'} onValueChange={(value) => handleStatusFilter(value === 'all' ? undefined : value as InventoryFilters['status'])}>
          <SelectTrigger className="w-40 border-[#e2dcd4]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="in-stock">In Stock</SelectItem>
            <SelectItem value="low-stock">Low Stock</SelectItem>
            <SelectItem value="out-of-stock">Out of Stock</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
          </SelectContent>
        </Select>

        {/* Supplier Filter */}
        <Select value={filters.supplier || 'all'} onValueChange={(value) => handleSupplierFilter(value === 'all' ? undefined : value)}>
          <SelectTrigger className="w-48 border-[#e2dcd4]">
            <SelectValue placeholder="Supplier" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Suppliers</SelectItem>
            {suppliers.map((supplier) => (
              <SelectItem key={supplier} value={supplier}>
                {supplier}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Sort */}
        <Select value={`${filters.sort_by}-${filters.sort_order}`} onValueChange={(value) => {
          const [sort_by, sort_order] = value.split('-') as [InventoryFilters['sort_by'], InventoryFilters['sort_order']];
          handleSort(sort_by, sort_order);
        }}>
          <SelectTrigger className="w-48 border-[#e2dcd4]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name-asc">Name A-Z</SelectItem>
            <SelectItem value="name-desc">Name Z-A</SelectItem>
            <SelectItem value="currentStock-asc">Stock Low-High</SelectItem>
            <SelectItem value="currentStock-desc">Stock High-Low</SelectItem>
            <SelectItem value="totalValue-desc">Value High-Low</SelectItem>
            <SelectItem value="totalValue-asc">Value Low-High</SelectItem>
            <SelectItem value="expiryDate-asc">Expiry Soon-Late</SelectItem>
            <SelectItem value="expiryDate-desc">Expiry Late-Soon</SelectItem>
          </SelectContent>
        </Select>

        {/* Reset Filters */}
        <Button
          variant="outline"
          onClick={resetFilters}
          className="border-[#e2dcd4] text-[#181510]"
        >
          <Filter className="w-4 h-4 mr-2" />
          Reset
        </Button>
      </div>

      {/* Inventory Table */}
      <div className="bg-white rounded-lg border border-[#e2dcd4] overflow-hidden">
        {items.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
            <h3 className="text-lg font-medium text-[#181510] mb-2">No inventory items found</h3>
            <p className="text-[#8a745c] mb-4">
              {searchTerm || filters.category || filters.status || filters.supplier
                ? 'Try adjusting your search or filters'
                : 'Start by adding your first inventory item'}
            </p>
            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/add`}>
              <Button className="bg-[#e58219] hover:bg-[#d4751a] text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add First Item
              </Button>
            </Link>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow className="border-[#e2dcd4]">
                <TableHead className="text-[#8a745c]">Item</TableHead>
                <TableHead className="text-[#8a745c]">Category</TableHead>
                <TableHead className="text-[#8a745c]">Stock</TableHead>
                <TableHead className="text-[#8a745c]">Status</TableHead>
                <TableHead className="text-[#8a745c]">Value</TableHead>
                <TableHead className="text-[#8a745c]">Supplier</TableHead>
                <TableHead className="text-[#8a745c]">Expiry</TableHead>
                <TableHead className="text-[#8a745c]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => (
                <TableRow key={item.id} className="border-[#e2dcd4]">
                  <TableCell>
                    <div>
                      <div className="font-medium text-[#181510]">{item.name}</div>
                      <div className="text-sm text-[#8a745c]">SKU: {item.sku}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="border-[#e2dcd4] text-[#8a745c]">
                      {item.category}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-[#181510]">
                        {item.currentStock} {item.unit}
                      </div>
                      <div className="text-sm text-[#8a745c]">
                        Min: {item.minStock} | Max: {item.maxStock}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(item.status)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium text-[#181510]">{formatCurrency(item.totalValue)}</div>
                      <div className="text-sm text-[#8a745c]">
                        {formatCurrency(item.costPerUnit)}/{item.unit}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-[#8a745c]">{item.supplier}</div>
                  </TableCell>
                  <TableCell>
                    {item.expiryDate ? (
                      <div className={cn(
                        "text-sm",
                        isExpiringSoon(item.expiryDate) ? "text-orange-600 font-medium" : "text-[#8a745c]"
                      )}>
                        {formatDate(item.expiryDate)}
                        {isExpiringSoon(item.expiryDate) && (
                          <div className="text-xs text-orange-600">Expiring soon!</div>
                        )}
                      </div>
                    ) : (
                      <span className="text-[#8a745c]">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/${item.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/${item.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Item
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => onDeleteItem(item.id)}
                          disabled={isDeleting}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {isDeleting ? 'Deleting...' : 'Delete Item'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-[#8a745c]">
            Showing {((currentPage - 1) * filters.limit) + 1} to {Math.min(currentPage * filters.limit, totalCount)} of {totalCount} items
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="border-[#e2dcd4] text-[#181510]"
            >
              Previous
            </Button>
            <span className="text-sm text-[#8a745c]">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="border-[#e2dcd4] text-[#181510]"
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
