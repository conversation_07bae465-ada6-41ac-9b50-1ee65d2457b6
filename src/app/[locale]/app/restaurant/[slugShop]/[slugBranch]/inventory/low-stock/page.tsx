'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  AlertTriangle, 
  ArrowLeft,
  ShoppingCart,
  Package,
  TrendingDown,
  RefreshCw,
  Download,
  Plus,
  Eye,
  Edit
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types
interface LowStockItem {
  id: string;
  name: string;
  category: string;
  sku: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  supplier: string;
  lastRestocked: Date;
  daysUntilEmpty: number;
  recommendedOrder: number;
  urgency: 'critical' | 'high' | 'medium';
}

export default function LowStockPage() {
  const t = useTranslations('inventory');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [items, setItems] = useState<LowStockItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Mock data
  const mockLowStockItems: LowStockItem[] = [
    {
      id: '2',
      name: 'Chicken Breast',
      category: 'Meat',
      sku: 'MEAT-001',
      currentStock: 5,
      minStock: 8,
      maxStock: 30,
      unit: 'kg',
      costPerUnit: 12.99,
      supplier: 'Premium Meats Ltd.',
      lastRestocked: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      daysUntilEmpty: 2,
      recommendedOrder: 25,
      urgency: 'critical',
    },
    {
      id: '3',
      name: 'Olive Oil',
      category: 'Oils & Condiments',
      sku: 'OIL-001',
      currentStock: 0,
      minStock: 5,
      maxStock: 20,
      unit: 'bottles',
      costPerUnit: 8.99,
      supplier: 'Mediterranean Imports',
      lastRestocked: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      daysUntilEmpty: 0,
      recommendedOrder: 15,
      urgency: 'critical',
    },
    {
      id: '6',
      name: 'Onions',
      category: 'Vegetables',
      sku: 'VEG-002',
      currentStock: 8,
      minStock: 15,
      maxStock: 40,
      unit: 'kg',
      costPerUnit: 2.50,
      supplier: 'Fresh Farm Co.',
      lastRestocked: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      daysUntilEmpty: 5,
      recommendedOrder: 32,
      urgency: 'high',
    },
    {
      id: '7',
      name: 'Pasta',
      category: 'Dry Goods',
      sku: 'DRY-001',
      currentStock: 12,
      minStock: 20,
      maxStock: 50,
      unit: 'kg',
      costPerUnit: 3.25,
      supplier: 'Italian Imports',
      lastRestocked: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
      daysUntilEmpty: 8,
      recommendedOrder: 38,
      urgency: 'medium',
    },
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setItems(mockLowStockItems);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Get urgency badge
  const getUrgencyBadge = (urgency: LowStockItem['urgency']) => {
    const urgencyConfig = {
      critical: { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
      high: { color: 'bg-orange-100 text-orange-800', icon: TrendingDown },
      medium: { color: 'bg-yellow-100 text-yellow-800', icon: Package },
    };

    const config = urgencyConfig[urgency];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {urgency.charAt(0).toUpperCase() + urgency.slice(1)}
      </Badge>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric'
    });
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  // Handle select all
  const toggleSelectAll = () => {
    setSelectedItems(prev => 
      prev.length === items.length ? [] : items.map(item => item.id)
    );
  };

  // Calculate totals
  const totalRecommendedCost = items
    .filter(item => selectedItems.includes(item.id))
    .reduce((sum, item) => sum + (item.recommendedOrder * item.costPerUnit), 0);

  const criticalItems = items.filter(item => item.urgency === 'critical').length;
  const highItems = items.filter(item => item.urgency === 'high').length;
  const mediumItems = items.filter(item => item.urgency === 'medium').length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Inventory
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-8 h-8 text-red-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Low Stock Alerts</h1>
              <p className="text-gray-600">Items that need immediate attention</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          {selectedItems.length > 0 && (
            <Button className="flex items-center gap-2">
              <ShoppingCart className="w-4 h-4" />
              Create Purchase Order ({selectedItems.length})
            </Button>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{criticalItems}</div>
            <p className="text-xs text-muted-foreground">
              Immediate action required
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            <TrendingDown className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{highItems}</div>
            <p className="text-xs text-muted-foreground">
              Order within 3 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Medium Priority</CardTitle>
            <Package className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{mediumItems}</div>
            <p className="text-xs text-muted-foreground">
              Order within a week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Selected Cost</CardTitle>
            <ShoppingCart className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRecommendedCost.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              {selectedItems.length} items selected
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Low Stock Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Low Stock Items</h3>
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={selectedItems.length === items.length}
                onChange={toggleSelectAll}
                className="rounded border-gray-300"
              />
              <label className="text-sm text-gray-600">Select All</label>
            </div>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Select</TableHead>
              <TableHead>Item</TableHead>
              <TableHead>Current Stock</TableHead>
              <TableHead>Urgency</TableHead>
              <TableHead>Days Until Empty</TableHead>
              <TableHead>Recommended Order</TableHead>
              <TableHead>Cost</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id} className={cn(
                item.urgency === 'critical' && 'bg-red-50',
                item.urgency === 'high' && 'bg-orange-50'
              )}>
                <TableCell>
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={() => toggleItemSelection(item.id)}
                    className="rounded border-gray-300"
                  />
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-sm text-gray-500">
                      {item.category} • SKU: {item.sku}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium text-red-600">
                      {item.currentStock} {item.unit}
                    </div>
                    <div className="text-sm text-gray-500">
                      Min: {item.minStock} {item.unit}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{getUrgencyBadge(item.urgency)}</TableCell>
                <TableCell>
                  <div className={cn(
                    "font-medium",
                    item.daysUntilEmpty === 0 ? "text-red-600" :
                    item.daysUntilEmpty <= 2 ? "text-orange-600" :
                    "text-yellow-600"
                  )}>
                    {item.daysUntilEmpty === 0 ? 'Out of stock' : `${item.daysUntilEmpty} days`}
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {item.recommendedOrder} {item.unit}
                    </div>
                    <div className="text-sm text-gray-500">
                      To reach max stock
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      ${(item.recommendedOrder * item.costPerUnit).toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-500">
                      ${item.costPerUnit.toFixed(2)}/{item.unit}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{item.supplier}</div>
                  <div className="text-xs text-gray-500">
                    Last: {formatDate(item.lastRestocked)}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/${item.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                    </Link>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => toggleItemSelection(item.id)}
                      className={cn(
                        selectedItems.includes(item.id) && "bg-blue-50 border-blue-300"
                      )}
                    >
                      <ShoppingCart className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {items.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No low stock items</h3>
          <p className="text-gray-600 mb-4">
            All inventory items are currently above minimum stock levels
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory`}>
            <Button>
              <Package className="w-4 h-4 mr-2" />
              View All Inventory
            </Button>
          </Link>
        </div>
      )}

      {/* Action Bar */}
      {selectedItems.length > 0 && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-600">
              {selectedItems.length} items selected • Total: ${totalRecommendedCost.toFixed(2)}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => setSelectedItems([])}>
                Clear Selection
              </Button>
              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/purchase-orders/create?items=${selectedItems.join(',')}`}>
                <Button className="flex items-center gap-2">
                  <ShoppingCart className="w-4 h-4" />
                  Create Purchase Order
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
