'use client';

import { useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { RestaurantList } from '@/components/restaurant/RestaurantList';
import { useRestaurants } from '@/hooks/useRestaurant';
import { MESSAGES } from '@/lib/constants/messages';
import { AppLoading } from '@/components/ui/app-loading';
import { RestaurantDeleteDialog } from '@/components/ui/delete-confirmation-dialog';
import { Shop } from '@/lib/types/shop';

export default function RestaurantShopsPage() {
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [restaurantToDelete, setRestaurantToDelete] = useState<Shop | null>(null);

  const {
    restaurants,
    isLoading,
    totalCount,
    statusCounts,
    cityCounts,
    filters,
    updateFilters,
    deleteRestaurant,
    isDeleting,
  } = useRestaurants({
    page: 1,
    limit: 12
  });

  const handleCreateRestaurant = () => {
    router.push('/app/restaurant/new');
  };

  const handleViewRestaurant = (restaurant: any) => {
    router.push(`/app/restaurant/${restaurant.slug}`);
  };

  const handleEditRestaurant = (restaurant: any) => {
    router.push(`/app/restaurant/${restaurant.slug}/settings`);
  };

  const handleDeleteRestaurant = (restaurant: Shop) => {
    setRestaurantToDelete(restaurant);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteRestaurant = async () => {
    if (restaurantToDelete) {
      await deleteRestaurant(restaurantToDelete.id);
      setDeleteDialogOpen(false);
      setRestaurantToDelete(null);
    }
  };

  if (isLoading) {
    return <AppLoading />;
  }

  return (
    <>
      <div className="min-h-screen bg-[#fbfaf9] p-6">
        <RestaurantList
          restaurants={restaurants}
          isLoading={isLoading}
          totalCount={totalCount}
          statusCounts={statusCounts}
          cityCounts={cityCounts}
          filters={filters}
          onFiltersChange={updateFilters}
          onRestaurantView={handleViewRestaurant}
          onRestaurantEdit={handleEditRestaurant}
          onRestaurantDelete={handleDeleteRestaurant}
          onRestaurantCreate={handleCreateRestaurant}
          showFilters={true}
          showCreateButton={true}
        />
      </div>

      {/* Delete Confirmation Dialog */}
      <RestaurantDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDeleteRestaurant}
        restaurantName={restaurantToDelete?.name}
        isLoading={isDeleting}
      />
    </>
  );
}
