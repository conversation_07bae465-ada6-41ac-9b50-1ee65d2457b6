'use client';

import { useAppSelector } from '@/lib/redux/hooks';
import { merchantApi } from '@/lib/redux/api/endpoints/restaurant/shopApi';

export default function DashboardPage() {
  const { user } = useAppSelector((state) => state.auth);
  const { data: merchants, isLoading } = merchantApi.useGetShopsQuery();

  if (isLoading) {
    return <div>Loading dashboard data...</div>;
  }

  // Count merchants by type
  const merchantCounts = merchants?.reduce((acc, merchant) => {
    acc[merchant.type] = (acc[merchant.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>) || {};

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Total Merchants Card */}
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold text-gray-700">Total Merchants</h2>
          <p className="text-3xl font-bold mt-2">{merchants?.length || 0}</p>
        </div>

        {/* Active Merchants Card */}
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold text-gray-700">Active Merchants</h2>
          <p className="text-3xl font-bold mt-2">
            {merchants?.filter(m => m.status === 'active').length || 0}
          </p>
        </div>

        {/* Pending Merchants Card */}
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold text-gray-700">Pending Merchants</h2>
          <p className="text-3xl font-bold mt-2">
            {merchants?.filter(m => m.status === 'pending').length || 0}
          </p>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">Merchants by Type</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-md">
            <h3 className="font-medium">Restaurants</h3>
            <p className="text-2xl font-bold mt-1">{merchantCounts.restaurant || 0}</p>
          </div>
          <div className="p-4 border rounded-md">
            <h3 className="font-medium">Retail</h3>
            <p className="text-2xl font-bold mt-1">{merchantCounts.retail || 0}</p>
          </div>
          <div className="p-4 border rounded-md">
            <h3 className="font-medium">Service</h3>
            <p className="text-2xl font-bold mt-1">{merchantCounts.service || 0}</p>
          </div>
          <div className="p-4 border rounded-md">
            <h3 className="font-medium">Digital</h3>
            <p className="text-2xl font-bold mt-1">{merchantCounts.digital || 0}</p>
          </div>
          <div className="p-4 border rounded-md">
            <h3 className="font-medium">Convenience</h3>
            <p className="text-2xl font-bold mt-1">{merchantCounts.convenience || 0}</p>
          </div>
          <div className="p-4 border rounded-md">
            <h3 className="font-medium">Custom</h3>
            <p className="text-2xl font-bold mt-1">{merchantCounts.custom || 0}</p>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">Recent Activity</h2>
        <p className="text-gray-500">No recent activity to display.</p>
      </div>
    </div>
  );
}
