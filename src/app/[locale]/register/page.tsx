'use client';

import { useState, FormEvent } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useRegister } from '@/hooks/useRegister';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { Link } from '@/i18n/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, UserPlus } from 'lucide-react';
import OAuthButtons from '@/components/auth/OAuthButtons';

export default function RegisterPage() {
  const t = useTranslations('auth');
  const locale = useLocale();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);
  const router = useRouter();
  const { register, isLoading, error, success, clearMessages } = useRegister();

  const handleRegister = async (e: FormEvent) => {
    e.preventDefault();
    clearMessages();
    setValidationError(null);

    // Validate passwords match
    if (password !== confirmPassword) {
      setValidationError('Passwords do not match');
      return;
    }

    // Call the register function from our hook
    const result = await register({
      name,
      email,
      password,
    });

    if (result) {
      // Registration successful - redirect to login after a delay
      setTimeout(() => {
        router.push(`/${locale}/login?message=registration-success`);
      }, 3000);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-[#fbfaf9] py-12 px-4 sm:px-6 lg:px-8">
      <div className="absolute top-4 right-4">
        <LanguageSwitcher />
      </div>

      <div className="flex-grow flex items-center justify-center">
        <div className="max-w-md w-full">
          <Card className="bg-[#f1edea] border-[#e5e1dc] shadow-lg">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-extrabold text-[#181510] flex items-center justify-center gap-2">
                <UserPlus className="h-8 w-8 text-[#8a745c]" />
                {t('register')}
              </CardTitle>
              <p className="mt-2 text-sm text-[#8a745c]">
                {t('alreadyHaveAccount')}{' '}
                <Link href="/login" className="font-medium text-[#8a745c] hover:text-[#6d5a48] underline">
                  {t('login')}
                </Link>
              </p>
            </CardHeader>
            <CardContent className="space-y-6">

              {(error || validationError) && (
                <Alert className="bg-red-50 border-red-200 text-red-800">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription>{error || validationError}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 border-green-200 text-green-800">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <form className="space-y-4" onSubmit={handleRegister}>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-[#181510] font-medium">
                      {t('name')}
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      autoComplete="name"
                      required
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="bg-[#fbfaf9] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c] focus:border-[#8a745c] focus:ring-[#8a745c]"
                      placeholder={t('name')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-address" className="text-[#181510] font-medium">
                      {t('email')}
                    </Label>
                    <Input
                      id="email-address"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-[#fbfaf9] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c] focus:border-[#8a745c] focus:ring-[#8a745c]"
                      placeholder={t('email')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-[#181510] font-medium">
                      {t('password')}
                    </Label>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="bg-[#fbfaf9] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c] focus:border-[#8a745c] focus:ring-[#8a745c]"
                      placeholder={t('password')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password" className="text-[#181510] font-medium">
                      {t('confirmPassword')}
                    </Label>
                    <Input
                      id="confirm-password"
                      name="confirm-password"
                      type="password"
                      autoComplete="new-password"
                      required
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="bg-[#fbfaf9] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c] focus:border-[#8a745c] focus:ring-[#8a745c]"
                      placeholder={t('confirmPassword')}
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-[#8a745c] hover:bg-[#6d5a48] text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8a745c] disabled:bg-[#a89a8c] disabled:cursor-not-allowed"
                >
                  {isLoading ? t('loading') : t('signUp')}
                </Button>
              </form>

              {/* OAuth Sign-in Options */}
              <OAuthButtons callbackUrl={`/${locale}/app`} />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
