'use client'

import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  User,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  LogOut,
  LogIn
} from 'lucide-react'
import { Link } from '@/i18n/navigation'

export default function AuthStatus() {
  const { user, isAuthenticated, isLoading, logout, session } = useAuth()

  if (isLoading) {
    return (
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#181510]">
            <Clock className="h-5 w-5 text-[#8a745c]" />
            Authentication Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 bg-[#f1edea] rounded-full animate-pulse" />
            <div className="space-y-2">
              <div className="h-4 w-32 bg-[#f1edea] rounded animate-pulse" />
              <div className="h-3 w-24 bg-[#f1edea] rounded animate-pulse" />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (isAuthenticated && user) {
    return (
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#181510]">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Authentication Status
            <Badge className="bg-green-100 text-green-800 border-green-200">
              Authenticated
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* User Info */}
          <div className="flex items-center gap-3">
            <div className="h-12 w-12 bg-gradient-to-br from-[#8a745c] to-[#6d5a48] rounded-full flex items-center justify-center text-white text-lg font-medium">
              {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
            </div>
            <div>
              <div className="font-medium text-[#181510]">
                {user.name || 'User'}
              </div>
              <div className="text-sm text-[#8a745c]">
                {user.email}
              </div>
            </div>
          </div>

          {/* User Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-[#8a745c]" />
                <span className="text-sm font-medium text-[#181510]">User ID</span>
              </div>
              <div className="text-sm text-[#8a745c] font-mono bg-[#f1edea] px-2 py-1 rounded">
                {user.id}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-[#8a745c]" />
                <span className="text-sm font-medium text-[#181510]">Role</span>
              </div>
              <Badge
                variant="outline"
                className="border-[#e5e1dc] text-[#8a745c] bg-[#f9f7f4] capitalize"
              >
                {user.role}
              </Badge>
            </div>
          </div>

          {/* Session Info */}
          {session && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-[#181510]">Session Details</div>
              <div className="text-xs text-[#8a745c] space-y-1">
                <div>Expires: {new Date(session.expires).toLocaleString()}</div>
                {session.user?.image && (
                  <div>Profile Image: Available</div>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-2 pt-2 border-t border-[#e5e1dc]">
            <Link href="/app/profile">
              <Button
                variant="outline"
                size="sm"
                className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
              >
                <User className="h-4 w-4 mr-2" />
                Profile
              </Button>
            </Link>
            <Button
              onClick={logout}
              variant="outline"
              size="sm"
              className="border-red-200 text-red-600 hover:bg-red-50"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-[#181510]">
          <XCircle className="h-5 w-5 text-amber-600" />
          Authentication Status
          <Badge className="bg-amber-100 text-amber-800 border-amber-200">
            Not Authenticated
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <AlertCircle className="h-8 w-8 text-amber-600" />
          <div>
            <div className="font-medium text-[#181510]">
              Authentication Required
            </div>
            <div className="text-sm text-[#8a745c]">
              Please sign in to access your account
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2 pt-2 border-t border-[#e5e1dc]">
          <Link href="/login">
            <Button
              size="sm"
              className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
            >
              <LogIn className="h-4 w-4 mr-2" />
              Sign In
            </Button>
          </Link>
          <Link href="/register">
            <Button
              variant="outline"
              size="sm"
              className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
            >
              Create Account
            </Button>
          </Link>
        </div>

        <div className="text-xs text-[#8a745c] bg-[#f9f7f4] p-3 rounded">
          <strong>For Testing:</strong> Visit <code className="bg-[#f1edea] px-1 rounded">/login</code> to test authentication flow
        </div>
      </CardContent>
    </Card>
  )
}
