'use client'

import { signIn } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { useState } from 'react'
import {
  Github,
  Chrome,
  Facebook,
  MessageCircle,
  Loader2
} from 'lucide-react'

interface OAuthButtonsProps {
  callbackUrl?: string
  className?: string
}

export default function OAuthButtons({ callbackUrl = '/app', className }: OAuthButtonsProps) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null)

  const handleOAuthSignIn = async (provider: string) => {
    try {
      setLoadingProvider(provider)
      await signIn(provider, {
        callbackUrl,
        redirect: true
      })
    } catch (error) {
      console.error(`${provider} sign-in error:`, error)
    } finally {
      setLoadingProvider(null)
    }
  }

  const providers = [
    {
      id: 'google',
      name: 'Google',
      icon: Chrome,
      bgColor: 'bg-white hover:bg-gray-50',
      textColor: 'text-gray-900',
      borderColor: 'border-gray-300'
    },
    {
      id: 'github',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      icon: Github,
      bgColor: 'bg-gray-900 hover:bg-gray-800',
      textColor: 'text-white',
      borderColor: 'border-gray-900'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: Facebook,
      bgColor: 'bg-blue-600 hover:bg-blue-700',
      textColor: 'text-white',
      borderColor: 'border-blue-600'
    },
    {
      id: 'discord',
      name: 'Discord',
      icon: MessageCircle,
      bgColor: 'bg-indigo-600 hover:bg-indigo-700',
      textColor: 'text-white',
      borderColor: 'border-indigo-600'
    }
  ]

  return (
    <div className={className}>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-[#f1edea] px-2 text-[#8a745c]">Or continue with</span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3 mt-6">
        {providers.map((provider) => {
          const Icon = provider.icon
          const isLoading = loadingProvider === provider.id

          return (
            <Button
              key={provider.id}
              variant="outline"
              onClick={() => handleOAuthSignIn(provider.id)}
              disabled={isLoading || loadingProvider !== null}
              className={`
                ${provider.bgColor}
                ${provider.textColor}
                border-2 ${provider.borderColor}
                font-medium py-2 px-4 rounded-md
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8a745c]
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors duration-200
              `}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Icon className="h-4 w-4 mr-2" />
              )}
              {provider.name}
            </Button>
          )
        })}
      </div>

      <div className="grid grid-cols-1 gap-3 mt-3">
        <Button
          variant="outline"
          onClick={() => handleOAuthSignIn('google')}
          disabled={loadingProvider !== null}
          className="w-full bg-white hover:bg-gray-50 text-gray-900 border-2 border-gray-300 font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8a745c] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {loadingProvider === 'google' ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Chrome className="h-4 w-4 mr-2" />
          )}
          Continue with Google
        </Button>
      </div>
    </div>
  )
}
