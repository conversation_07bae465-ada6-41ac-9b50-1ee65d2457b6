'use client';

import React, { useState } from 'react';

interface FallbackImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  style?: React.CSSProperties;
}

export const FallbackImage: React.FC<FallbackImageProps> = ({
  src,
  alt,
  className = '',
  fallbackSrc = 'https://via.placeholder.com/400x400/f4f2f1/161412?text=No+Image',
  style,
}) => {
  const [imageSrc, setImageSrc] = useState(src);
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError) {
      setHasError(true);
      setImageSrc(fallbackSrc);
    }
  };

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      style={style}
      onError={handleError}
    />
  );
};

interface FallbackBackgroundImageProps {
  src: string;
  className?: string;
  fallbackSrc?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export const FallbackBackgroundImage: React.FC<FallbackBackgroundImageProps> = ({
  src,
  className = '',
  fallbackSrc = 'https://via.placeholder.com/400x400/f4f2f1/161412?text=No+Image',
  style,
  children,
}) => {
  const [backgroundImage, setBackgroundImage] = useState(`url("${src}")`);
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError) {
      setHasError(true);
      setBackgroundImage(`url("${fallbackSrc}")`);
    }
  };

  // Create a hidden image to test if the source loads
  React.useEffect(() => {
    const img = new Image();
    img.onload = () => {
      // Image loaded successfully
      setBackgroundImage(`url("${src}")`);
    };
    img.onerror = () => {
      // Image failed to load
      handleError();
    };
    img.src = src;
  }, [src]);

  return (
    <div
      className={className}
      style={{
        ...style,
        backgroundImage,
      }}
    >
      {children}
    </div>
  );
};

export default FallbackImage;
