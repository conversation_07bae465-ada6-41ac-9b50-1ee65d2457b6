'use client';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetServicesQuery, useGetAppointmentsQuery, useGetStaffQuery } from '@/lib/redux/api/endpoints/serviceApi';
import { useState } from 'react';
import Link from 'next/link';

interface ServiceDashboardProps {
  merchantId: string;
}

export default function ServiceDashboard({ merchantId }: ServiceDashboardProps) {
  const { data: merchant, isLoading: merchantLoading } = useGetMerchantsQuery(undefined, {
    selectFromResult: (result) => ({
      ...result,
      data: result.data?.find(m => m.id === merchantId && m.type === 'service')
    })
  });

  const { data: services, isLoading: servicesLoading } = useGetServicesQuery(merchantId);
  const { data: appointments, isLoading: appointmentsLoading } = useGetAppointmentsQuery(merchantId);
  const { data: staff, isLoading: staffLoading } = useGetStaffQuery(merchantId);

  const [activeTab, setActiveTab] = useState<'overview' | 'services' | 'appointments' | 'staff'>('overview');

  if (merchantLoading || servicesLoading || appointmentsLoading || staffLoading) {
    return <div className="p-4">Loading service data...</div>;
  }

  if (!merchant) {
    return <div className="p-4">Service not found or not a service type merchant.</div>;
  }

  // Get today's appointments
  const today = new Date().toISOString().split('T')[0];
  const todayAppointments = appointments?.filter(a => a.date === today) || [];

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-6 border-b">
        <div className="flex items-center">
          {merchant.logo && (
            <img
              src={merchant.logo}
              alt={merchant.name}
              className="h-16 w-16 rounded-full mr-4 object-cover"
            />
          )}
          <div>
            <h1 className="text-2xl font-bold">{merchant.name}</h1>
            <p className="text-gray-600">{merchant.address}</p>
          </div>
        </div>
      </div>

      <div className="border-b">
        <nav className="flex">
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'services' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('services')}
          >
            Services
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'appointments' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('appointments')}
          >
            Appointments
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'staff' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('staff')}
          >
            Staff
          </button>
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'overview' && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Service Overview</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Services Offered</h3>
                <p className="text-2xl font-bold">{services?.length || 0}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Today's Appointments</h3>
                <p className="text-2xl font-bold">{todayAppointments.length}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Staff Members</h3>
                <p className="text-2xl font-bold">{staff?.length || 0}</p>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="font-medium mb-2">Service Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Appointment Booking</h4>
                  <p>{merchant.settings?.appointmentEnabled ? 'Enabled' : 'Disabled'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Cancellation Policy</h4>
                  <p>{merchant.settings?.cancellationPolicy || 'Not specified'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Service Locations</h4>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {merchant.settings?.serviceLocations?.map((location, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        {location}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Business Hours</h4>
                  <div className="text-sm">
                    {merchant.settings?.openingHours ? (
                      Object.entries(merchant.settings.openingHours).map(([day, hours]) => (
                        <div key={day} className="flex justify-between">
                          <span className="font-medium">{day}</span>
                          <span>{hours.open} - {hours.close}</span>
                        </div>
                      ))
                    ) : (
                      <p>Not specified</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'services' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Services</h2>
              <Link
                href={`/admin/merchants/service/${merchantId}/services/create`}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Add Service
              </Link>
            </div>

            {services && services.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {services.map((service) => (
                      <tr key={service.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {service.image && (
                              <img
                                src={service.image}
                                alt={service.name}
                                className="h-10 w-10 rounded-full mr-3 object-cover"
                              />
                            )}
                            <div>
                              <div className="font-medium text-gray-900">{service.name}</div>
                              <div className="text-sm text-gray-500">{service.category}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {service.duration} min
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          ${service.price.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            service.available
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {service.available ? 'Available' : 'Unavailable'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/admin/merchants/service/${merchantId}/services/${service.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900 mr-3"
                          >
                            Edit
                          </Link>
                          <button className="text-red-600 hover:text-red-900">
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No services found. Add your first service to get started.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'appointments' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Appointments</h2>
              <Link
                href={`/admin/merchants/service/${merchantId}/appointments/create`}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Create Appointment
              </Link>
            </div>

            {appointments && appointments.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date & Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {appointments.map((appointment) => (
                      <tr key={appointment.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="font-medium">{appointment.date}</div>
                          <div className="text-gray-500">{appointment.time}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {services?.find(s => s.id === appointment.serviceId)?.name || 'Unknown Service'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {appointment.userId}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            appointment.status === 'confirmed'
                              ? 'bg-green-100 text-green-800'
                              : appointment.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : appointment.status === 'cancelled'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/admin/merchants/service/${merchantId}/appointments/${appointment.id}`}
                            className="text-indigo-600 hover:text-indigo-900 mr-3"
                          >
                            View
                          </Link>
                          <button className="text-red-600 hover:text-red-900">
                            Cancel
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No appointments found.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'staff' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Staff</h2>
              <Link
                href={`/admin/merchants/service/${merchantId}/staff/create`}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Add Staff Member
              </Link>
            </div>

            {staff && staff.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {staff.map((member) => (
                  <div key={member.id} className="border rounded-lg overflow-hidden">
                    <div className="p-4">
                      <div className="flex items-center mb-3">
                        {member.image ? (
                          <img
                            src={member.image}
                            alt={member.name}
                            className="h-12 w-12 rounded-full mr-3 object-cover"
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                            <span className="text-gray-500 text-lg">{member.name.charAt(0)}</span>
                          </div>
                        )}
                        <div>
                          <h3 className="font-medium">{member.name}</h3>
                          <p className="text-sm text-gray-500">{member.role}</p>
                        </div>
                      </div>

                      <div className="text-sm mb-3">
                        <div className="flex items-center mb-1">
                          <span className="mr-2">📧</span>
                          <span>{member.email}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="mr-2">📱</span>
                          <span>{member.phone}</span>
                        </div>
                      </div>

                      {member.specialties && member.specialties.length > 0 && (
                        <div className="mb-3">
                          <h4 className="text-xs font-medium text-gray-500 mb-1">Specialties</h4>
                          <div className="flex flex-wrap gap-1">
                            {member.specialties.map((specialty, index) => (
                              <span key={index} className="px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {specialty}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end mt-2">
                        <Link
                          href={`/admin/merchants/service/${merchantId}/staff/${member.id}/edit`}
                          className="text-sm text-indigo-600 hover:text-indigo-900 mr-3"
                        >
                          Edit
                        </Link>
                        <button className="text-sm text-red-600 hover:text-red-900">
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No staff members found. Add your first staff member to get started.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
