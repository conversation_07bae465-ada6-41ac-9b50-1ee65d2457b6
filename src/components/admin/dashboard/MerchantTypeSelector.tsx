'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

type MerchantType = 'restaurant' | 'retail' | 'service' | 'digital' | 'custom' | 'convenience';

interface MerchantTypeOption {
  value: MerchantType;
  label: string;
  icon: string;
  description: string;
}

const merchantTypes: MerchantTypeOption[] = [
  {
    value: 'restaurant',
    label: 'Restaurant',
    icon: '🍽️',
    description: 'Food ordering, table reservations, menu management'
  },
  {
    value: 'retail',
    label: 'Retail',
    icon: '🛍️',
    description: 'Product inventory, shipping options'
  },
  {
    value: 'service',
    label: 'Service',
    icon: '🔧',
    description: 'Appointment booking, service providers'
  },
  {
    value: 'digital',
    label: 'Digital',
    icon: '💻',
    description: 'Digital product delivery'
  },
  {
    value: 'convenience',
    label: 'Convenience',
    icon: '🏪',
    description: 'Quick service retail'
  },
  {
    value: 'custom',
    label: 'Custom',
    icon: '🔄',
    description: 'Customizable shop type'
  }
];

interface MerchantTypeSelectorProps {
  onSelect?: (type: MerchantType) => void;
  selectedType?: MerchantType | null;
  onContinue?: () => void;
}

export default function MerchantTypeSelector({
  onSelect,
  selectedType: propSelectedType,
  onContinue
}: MerchantTypeSelectorProps = {}) {
  const [internalSelectedType, setInternalSelectedType] = useState<MerchantType | null>(propSelectedType || null);
  const router = useRouter();

  // Use either the prop value or the internal state
  const selectedType = propSelectedType !== undefined ? propSelectedType : internalSelectedType;

  const handleTypeSelect = (type: MerchantType) => {
    setInternalSelectedType(type);
    if (onSelect) {
      onSelect(type);
    }
  };

  const handleContinue = () => {
    if (onContinue) {
      onContinue();
    } else if (selectedType) {
      router.push(`/admin/merchants/${selectedType}`);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Select Merchant Type</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {merchantTypes.map((type) => (
          <div
            key={type.value}
            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
              selectedType === type.value
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-blue-300'
            }`}
            onClick={() => handleTypeSelect(type.value)}
          >
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-2">{type.icon}</span>
              <h3 className="font-medium">{type.label}</h3>
            </div>
            <p className="text-sm text-gray-600">{type.description}</p>
          </div>
        ))}
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleContinue}
          disabled={!selectedType}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </div>
  );
}
