'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { DigitalProduct, digitalProductSchema } from '@/lib/validations/digitalProductSchema';
import { 
  useCreateDigitalProductMutation,
  useUpdateDigitalProductMutation
} from '@/lib/redux/api/endpoints/digitalApi';

interface DigitalProductFormProps {
  merchantId: string;
  initialData?: DigitalProduct & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function DigitalProductForm({ 
  merchantId, 
  initialData, 
  onSuccess,
  mode
}: DigitalProductFormProps) {
  const [createDigitalProduct, { isLoading: isCreating }] = useCreateDigitalProductMutation();
  const [updateDigitalProduct, { isLoading: isUpdating }] = useUpdateDigitalProductMutation();
  
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const isLoading = isCreating || isUpdating;
  
  // Set up react-hook-form with zod validation
  const { 
    register, 
    handleSubmit, 
    control,
    formState: { errors, isDirty }
  } = useForm<DigitalProduct>({
    resolver: zodResolver(digitalProductSchema),
    defaultValues: initialData || {
      name: '',
      description: '',
      price: 0,
      salePrice: null,
      category: '',
      images: [],
      fileUrl: '',
      fileSize: 0,
      fileType: '',
      previewUrl: null,
      licenseType: '',
      version: '',
      requirements: '',
      features: [],
      available: true,
    }
  });
  
  // Set up field arrays for images and features
  const { 
    fields: imageFields, 
    append: appendImage, 
    remove: removeImage 
  } = useFieldArray({
    control,
    name: 'images',
  });
  
  const { 
    fields: featureFields, 
    append: appendFeature, 
    remove: removeFeature 
  } = useFieldArray({
    control,
    name: 'features',
  });
  
  // Form submission handler
  const onSubmit: SubmitHandler<DigitalProduct> = async (data) => {
    try {
      let result;
      
      if (mode === 'create') {
        // Create new digital product
        result = await createDigitalProduct({
          merchantId,
          ...data,
        }).unwrap();
        
        setSuccess('Digital product created successfully');
      } else {
        // Update existing digital product
        if (!initialData?.id) {
          throw new Error('Digital product ID is required for updates');
        }
        
        result = await updateDigitalProduct({
          merchantId,
          productId: initialData.id,
          data,
        }).unwrap();
        
        setSuccess('Digital product updated successfully');
      }
      
      setError(null);
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} digital product. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing digital product:`, err);
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">
        {mode === 'create' ? 'Add Digital Product' : 'Edit Digital Product'}
      </h2>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Product Name *
            </label>
            <input
              id="name"
              {...register('name')}
              className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category *
            </label>
            <input
              id="category"
              {...register('category')}
              className={`w-full p-2 border rounded-md ${errors.category ? 'border-red-500' : ''}`}
              placeholder="e.g., eBooks, Software, Templates"
            />
            {errors.category && (
              <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Regular Price *
            </label>
            <input
              id="price"
              type="number"
              step="0.01"
              {...register('price', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.price ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-1">
              Sale Price
            </label>
            <input
              id="salePrice"
              type="number"
              step="0.01"
              {...register('salePrice', { 
                setValueAs: v => v === '' ? null : parseFloat(v),
                valueAsNumber: true 
              })}
              className={`w-full p-2 border rounded-md ${errors.salePrice ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.salePrice && (
              <p className="mt-1 text-sm text-red-600">{errors.salePrice.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="fileUrl" className="block text-sm font-medium text-gray-700 mb-1">
              File URL *
            </label>
            <input
              id="fileUrl"
              type="url"
              {...register('fileUrl')}
              className={`w-full p-2 border rounded-md ${errors.fileUrl ? 'border-red-500' : ''}`}
            />
            {errors.fileUrl && (
              <p className="mt-1 text-sm text-red-600">{errors.fileUrl.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="previewUrl" className="block text-sm font-medium text-gray-700 mb-1">
              Preview URL
            </label>
            <input
              id="previewUrl"
              type="url"
              {...register('previewUrl', {
                setValueAs: v => v === '' ? null : v,
              })}
              className={`w-full p-2 border rounded-md ${errors.previewUrl ? 'border-red-500' : ''}`}
            />
            {errors.previewUrl && (
              <p className="mt-1 text-sm text-red-600">{errors.previewUrl.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="fileSize" className="block text-sm font-medium text-gray-700 mb-1">
              File Size (KB) *
            </label>
            <input
              id="fileSize"
              type="number"
              {...register('fileSize', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.fileSize ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.fileSize && (
              <p className="mt-1 text-sm text-red-600">{errors.fileSize.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="fileType" className="block text-sm font-medium text-gray-700 mb-1">
              File Type *
            </label>
            <input
              id="fileType"
              {...register('fileType')}
              className={`w-full p-2 border rounded-md ${errors.fileType ? 'border-red-500' : ''}`}
              placeholder="e.g., PDF, ZIP, MP4"
            />
            {errors.fileType && (
              <p className="mt-1 text-sm text-red-600">{errors.fileType.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="licenseType" className="block text-sm font-medium text-gray-700 mb-1">
              License Type *
            </label>
            <input
              id="licenseType"
              {...register('licenseType')}
              className={`w-full p-2 border rounded-md ${errors.licenseType ? 'border-red-500' : ''}`}
              placeholder="e.g., Standard, Extended, Commercial"
            />
            {errors.licenseType && (
              <p className="mt-1 text-sm text-red-600">{errors.licenseType.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="version" className="block text-sm font-medium text-gray-700 mb-1">
              Version
            </label>
            <input
              id="version"
              {...register('version')}
              className={`w-full p-2 border rounded-md ${errors.version ? 'border-red-500' : ''}`}
              placeholder="e.g., 1.0.0"
            />
            {errors.version && (
              <p className="mt-1 text-sm text-red-600">{errors.version.message}</p>
            )}
          </div>
          
          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>
          
          <div className="md:col-span-2">
            <label htmlFor="requirements" className="block text-sm font-medium text-gray-700 mb-1">
              Requirements
            </label>
            <textarea
              id="requirements"
              {...register('requirements')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.requirements ? 'border-red-500' : ''}`}
              placeholder="e.g., System requirements, prerequisites"
            />
            {errors.requirements && (
              <p className="mt-1 text-sm text-red-600">{errors.requirements.message}</p>
            )}
          </div>
          
          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                {...register('available')}
                className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Available</span>
            </label>
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Images
          </label>
          
          <div className="space-y-2 mb-2">
            {imageFields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`images.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="https://example.com/image.jpg"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={() => appendImage('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Image URL
          </button>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Features
          </label>
          
          <div className="space-y-2 mb-2">
            {featureFields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`features.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="e.g., Lifetime updates, 24/7 support"
                />
                <button
                  type="button"
                  onClick={() => removeFeature(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={() => appendFeature('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Feature
          </button>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Product' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
