'use client';

import { useState } from 'react';
import { useGetTemplatesQuery, useDeleteTemplateMutation } from '@/lib/redux/api/endpoints/communicationApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Edit, Trash2, Mail, MessageSquare, Plus } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

interface TemplateListProps {
  merchantId: string;
  onCreateTemplate: () => void;
  onEditTemplate: (templateId: string) => void;
}

export default function TemplateList({ merchantId, onCreateTemplate, onEditTemplate }: TemplateListProps) {
  const [activeTab, setActiveTab] = useState<'all' | 'email' | 'sms'>('all');
  // Using sonner toast

  // Fetch templates
  const { data: templates, isLoading, isError, refetch } = useGetTemplatesQuery({
    merchantId,
    type: activeTab === 'all' ? undefined : activeTab,
  });

  // Delete template mutation
  const [deleteTemplate, { isLoading: isDeleting }] = useDeleteTemplateMutation();

  // Handle delete template
  const handleDeleteTemplate = async (templateId: string) => {
    try {
      await deleteTemplate({ merchantId, templateId }).unwrap();
      toast.success('Template deleted', {
        description: 'The template has been deleted successfully.',
      });
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to delete the template. Please try again.',
      });
    }
  };

  // Get template icon based on type
  const getTemplateIcon = (type: 'email' | 'sms') => {
    return type === 'email' ? <Mail className="h-4 w-4" /> : <MessageSquare className="h-4 w-4" />;
  };

  // Get category badge color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'appointment_confirmation':
        return 'bg-green-100 text-green-800';
      case 'appointment_reminder':
        return 'bg-blue-100 text-blue-800';
      case 'appointment_cancellation':
        return 'bg-red-100 text-red-800';
      case 'marketing':
        return 'bg-purple-100 text-purple-800';
      case 'custom':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format category name for display
  const formatCategoryName = (category: string) => {
    return category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="w-full">
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-9 w-20 mr-2" />
              <Skeleton className="h-9 w-20" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Render error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Failed to load templates</h3>
        <p className="text-gray-500 mb-4">There was an error loading the communication templates.</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'all' | 'email' | 'sms')}>
          <TabsList>
            <TabsTrigger value="all">All Templates</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="sms">SMS</TabsTrigger>
          </TabsList>
        </Tabs>

        <Button onClick={onCreateTemplate}>
          <Plus className="h-4 w-4 mr-2" />
          New Template
        </Button>
      </div>

      {templates && templates.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {templates.map((template) => (
            <Card key={template.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <div className="flex items-center space-x-1">
                    {getTemplateIcon(template.type)}
                    <span className="text-xs font-medium capitalize">{template.type}</span>
                  </div>
                </div>
                <CardDescription>
                  <Badge className={`${getCategoryColor(template.category)} mt-2`}>
                    {formatCategoryName(template.category)}
                  </Badge>
                  {template.isDefault && (
                    <Badge variant="outline" className="ml-2 mt-2">
                      Default
                    </Badge>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-500 line-clamp-2">
                  {template.type === 'email' && template.subject ? `Subject: ${template.subject}` : ''}
                </p>
                <p className="text-sm text-gray-500 line-clamp-3 mt-1">
                  {template.content}
                </p>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between">
                <Button variant="outline" size="sm" onClick={() => onEditTemplate(template.id!)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm" className="text-red-500 border-red-200 hover:bg-red-50">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Template</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this template? This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleDeleteTemplate(template.id!)}
                        className="bg-red-500 hover:bg-red-600"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
            {activeTab === 'email' ? (
              <Mail className="h-6 w-6 text-gray-500" />
            ) : activeTab === 'sms' ? (
              <MessageSquare className="h-6 w-6 text-gray-500" />
            ) : (
              <AlertCircle className="h-6 w-6 text-gray-500" />
            )}
          </div>
          <h3 className="text-lg font-semibold mb-2">No templates found</h3>
          <p className="text-gray-500 mb-4">
            {activeTab === 'all'
              ? 'You haven\'t created any communication templates yet.'
              : `You haven\'t created any ${activeTab} templates yet.`}
          </p>
          <Button onClick={onCreateTemplate}>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>
      )}
    </div>
  );
}
