'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Home, Menu, Bell, User, Settings, Calendar, Utensils, Star } from 'lucide-react';

interface AppearancePreviewProps {
  theme: string;
  accentColor: string;
  fontSize: string;
  customFont: string;
  reducedMotion: boolean;
  highContrast: boolean;
  compactMode: boolean;
}

// Color theme options mapping
const colorThemesMap = {
  earth: { primary: '#8a745c', secondary: '#e5ccb2', bg: '#fbfaf9', text: '#181510' },
  ocean: { primary: '#3b82f6', secondary: '#93c5fd', bg: '#f0f9ff', text: '#1e3a8a' },
  forest: { primary: '#059669', secondary: '#a7f3d0', bg: '#ecfdf5', text: '#064e3b' },
  sunset: { primary: '#ea580c', secondary: '#fdba74', bg: '#fff7ed', text: '#7c2d12' },
  berry: { primary: '#8b5cf6', secondary: '#c4b5fd', bg: '#f5f3ff', text: '#4c1d95' },
};

// Font size options mapping
const fontSizeMap = {
  small: 0.875,
  medium: 1,
  large: 1.125,
  'x-large': 1.25,
};

// Font family options mapping
const fontFamilyMap = {
  'be-vietnam': 'Be Vietnam Pro, sans-serif',
  inter: 'Inter, sans-serif',
  roboto: 'Roboto, sans-serif',
  poppins: 'Poppins, sans-serif',
  montserrat: 'Montserrat, sans-serif',
};

export default function AppearancePreview({
  theme,
  accentColor,
  fontSize,
  customFont,
  reducedMotion,
  highContrast,
  compactMode,
}: AppearancePreviewProps) {
  // Get the selected color theme
  const colorTheme = colorThemesMap[accentColor as keyof typeof colorThemesMap] || colorThemesMap.earth;
  
  // Get the selected font size scale
  const fontSizeScale = fontSizeMap[fontSize as keyof typeof fontSizeMap] || fontSizeMap.medium;
  
  // Get the selected font family
  const fontFamily = fontFamilyMap[customFont as keyof typeof fontFamilyMap] || fontFamilyMap['be-vietnam'];

  // Determine background and text colors based on theme
  const bgColor = theme === 'dark' ? '#1c1917' : colorTheme.bg;
  const textColor = theme === 'dark' ? '#f5f5f4' : colorTheme.text;
  const secondaryTextColor = theme === 'dark' ? '#a8a29e' : colorTheme.primary;
  const primaryColor = theme === 'dark' ? colorTheme.secondary : colorTheme.primary;
  const secondaryColor = colorTheme.secondary;
  const borderColor = theme === 'dark' ? '#292524' : '#e5e1dc';

  // Apply high contrast if enabled
  const contrastModifier = highContrast ? 
    { textColor: theme === 'dark' ? '#ffffff' : '#000000', bgColor: theme === 'dark' ? '#000000' : '#ffffff' } 
    : {};

  // Apply compact mode spacing
  const spacing = compactMode ? 'space-y-2 p-3' : 'space-y-4 p-4';

  // Apply reduced motion
  const transitionStyle = reducedMotion ? 'transition-none' : 'transition-all duration-300';

  return (
    <Card className="overflow-hidden border-2 border-dashed border-[#e5e1dc]">
      <CardHeader className="bg-[#f1edea] pb-2">
        <CardTitle className="text-sm text-[#8a745c]">Live Preview</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div 
          className={cn("flex flex-col", transitionStyle)}
          style={{ 
            backgroundColor: contrastModifier.bgColor || bgColor,
            color: contrastModifier.textColor || textColor,
            fontFamily,
            fontSize: `${fontSizeScale}rem`,
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between border-b p-3" style={{ borderColor }}>
            <div className="flex items-center gap-2">
              <Menu size={16} style={{ color: secondaryTextColor }} />
              <span className="font-medium">Restaurant Dashboard</span>
            </div>
            <div className="flex items-center gap-3">
              <Bell size={16} style={{ color: secondaryTextColor }} />
              <Avatar className="h-6 w-6">
                <AvatarImage src="https://github.com/shadcn.png" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
            </div>
          </div>

          {/* Content */}
          <div className={spacing}>
            <div className="flex items-center justify-between px-3">
              <h3 className="font-bold" style={{ fontSize: `${fontSizeScale * 1.25}rem` }}>Dashboard</h3>
              <Button 
                className={transitionStyle}
                style={{ 
                  backgroundColor: secondaryColor,
                  color: textColor,
                }}
                size="sm"
              >
                <Settings size={14} className="mr-1" /> Settings
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3 px-3">
              <Card 
                className={transitionStyle}
                style={{ 
                  backgroundColor: theme === 'dark' ? '#292524' : '#ffffff',
                  borderColor,
                }}
              >
                <CardContent className={compactMode ? 'p-2' : 'p-3'}>
                  <div className="flex items-center gap-2">
                    <div 
                      className="rounded-full p-1"
                      style={{ backgroundColor: primaryColor }}
                    >
                      <Calendar size={16} style={{ color: theme === 'dark' ? textColor : '#ffffff' }} />
                    </div>
                    <div>
                      <div className="text-xs" style={{ color: secondaryTextColor }}>Reservations</div>
                      <div className="font-bold">12</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card 
                className={transitionStyle}
                style={{ 
                  backgroundColor: theme === 'dark' ? '#292524' : '#ffffff',
                  borderColor,
                }}
              >
                <CardContent className={compactMode ? 'p-2' : 'p-3'}>
                  <div className="flex items-center gap-2">
                    <div 
                      className="rounded-full p-1"
                      style={{ backgroundColor: primaryColor }}
                    >
                      <Utensils size={16} style={{ color: theme === 'dark' ? textColor : '#ffffff' }} />
                    </div>
                    <div>
                      <div className="text-xs" style={{ color: secondaryTextColor }}>Orders</div>
                      <div className="font-bold">8</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="px-3">
              <Card 
                className={transitionStyle}
                style={{ 
                  backgroundColor: theme === 'dark' ? '#292524' : '#ffffff',
                  borderColor,
                }}
              >
                <CardHeader className={compactMode ? 'p-2' : 'p-3 pb-0'}>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">Recent Reviews</CardTitle>
                    <Badge 
                      className={transitionStyle}
                      style={{ 
                        backgroundColor: secondaryColor,
                        color: textColor,
                      }}
                    >
                      New
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className={compactMode ? 'p-2 pt-0' : 'p-3 pt-0'}>
                  <div className="flex items-center gap-2 mt-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback>JD</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-xs font-medium">John Doe</div>
                      <div className="flex items-center">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star key={star} size={12} fill={primaryColor} color={primaryColor} />
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
