import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Simple component to test
const SimpleReservationCard = ({ 
  reservation, 
  onEdit, 
  onDelete 
}: { 
  reservation: any; 
  onEdit: (id: string) => void; 
  onDelete: (id: string) => void; 
}) => {
  return (
    <div data-testid={`reservation-${reservation.id}`} className="border rounded-lg p-4">
      <h3 data-testid="customer-name">{reservation.customerName}</h3>
      <p data-testid="customer-phone">{reservation.customerPhone}</p>
      <p data-testid="party-size">Party of {reservation.partySize}</p>
      <p data-testid="reservation-time">
        {reservation.reservationDate} at {reservation.reservationTime}
      </p>
      <p data-testid="table-name">{reservation.tableName}</p>
      <span 
        data-testid="status"
        className={`px-2 py-1 rounded text-xs ${
          reservation.status === 'confirmed' ? 'bg-green-100 text-green-800' :
          reservation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}
      >
        {reservation.status}
      </span>
      <div className="mt-2 space-x-2">
        <button 
          data-testid="edit-button"
          onClick={() => onEdit(reservation.id)}
          className="text-sm bg-blue-500 text-white px-2 py-1 rounded"
        >
          Edit
        </button>
        <button 
          data-testid="delete-button"
          onClick={() => onDelete(reservation.id)}
          className="text-sm bg-red-500 text-white px-2 py-1 rounded"
        >
          Delete
        </button>
      </div>
    </div>
  )
}

// Simple list component
const SimpleReservationList = ({ 
  reservations, 
  searchTerm, 
  onSearchChange,
  onEdit,
  onDelete 
}: { 
  reservations: any[]; 
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}) => {
  const filteredReservations = reservations.filter(reservation =>
    reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reservation.customerPhone.includes(searchTerm) ||
    reservation.tableName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div data-testid="reservation-list">
      <div className="mb-4">
        <input
          data-testid="search-input"
          type="text"
          placeholder="Search reservations..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full px-3 py-2 border rounded-md"
        />
      </div>
      
      <div className="space-y-4">
        {filteredReservations.length === 0 ? (
          <div data-testid="no-results" className="text-center py-8">
            <p>No reservations found</p>
          </div>
        ) : (
          filteredReservations.map((reservation) => (
            <SimpleReservationCard
              key={reservation.id}
              reservation={reservation}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))
        )}
      </div>
    </div>
  )
}

// Test data
const mockReservations = [
  {
    id: 'res-1',
    customerName: 'John Doe',
    customerPhone: '+1234567890',
    partySize: 4,
    reservationDate: '2024-01-15',
    reservationTime: '19:00',
    status: 'confirmed',
    tableName: 'Table 5',
  },
  {
    id: 'res-2',
    customerName: 'Jane Smith',
    customerPhone: '+1234567891',
    partySize: 2,
    reservationDate: '2024-01-15',
    reservationTime: '20:00',
    status: 'pending',
    tableName: 'Table 3',
  },
]

describe('SimpleReservationCard', () => {
  const mockOnEdit = jest.fn()
  const mockOnDelete = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders reservation information correctly', () => {
    const reservation = mockReservations[0]
    
    render(
      <SimpleReservationCard
        reservation={reservation}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('customer-name')).toHaveTextContent('John Doe')
    expect(screen.getByTestId('customer-phone')).toHaveTextContent('+1234567890')
    expect(screen.getByTestId('party-size')).toHaveTextContent('Party of 4')
    expect(screen.getByTestId('reservation-time')).toHaveTextContent('2024-01-15 at 19:00')
    expect(screen.getByTestId('table-name')).toHaveTextContent('Table 5')
    expect(screen.getByTestId('status')).toHaveTextContent('confirmed')
  })

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup()
    const reservation = mockReservations[0]
    
    render(
      <SimpleReservationCard
        reservation={reservation}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    await user.click(screen.getByTestId('edit-button'))
    expect(mockOnEdit).toHaveBeenCalledWith('res-1')
  })

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup()
    const reservation = mockReservations[0]
    
    render(
      <SimpleReservationCard
        reservation={reservation}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    await user.click(screen.getByTestId('delete-button'))
    expect(mockOnDelete).toHaveBeenCalledWith('res-1')
  })

  it('displays correct status styling', () => {
    const confirmedReservation = mockReservations[0]
    const pendingReservation = mockReservations[1]
    
    const { rerender } = render(
      <SimpleReservationCard
        reservation={confirmedReservation}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('status')).toHaveClass('bg-green-100', 'text-green-800')

    rerender(
      <SimpleReservationCard
        reservation={pendingReservation}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('status')).toHaveClass('bg-yellow-100', 'text-yellow-800')
  })
})

describe('SimpleReservationList', () => {
  const mockOnEdit = jest.fn()
  const mockOnDelete = jest.fn()
  const mockOnSearchChange = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders all reservations when no search term', () => {
    render(
      <SimpleReservationList
        reservations={mockReservations}
        searchTerm=""
        onSearchChange={mockOnSearchChange}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('reservation-res-1')).toBeInTheDocument()
    expect(screen.getByTestId('reservation-res-2')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
  })

  it('filters reservations by customer name', () => {
    render(
      <SimpleReservationList
        reservations={mockReservations}
        searchTerm="john"
        onSearchChange={mockOnSearchChange}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('reservation-res-1')).toBeInTheDocument()
    expect(screen.queryByTestId('reservation-res-2')).not.toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
  })

  it('filters reservations by phone number', () => {
    render(
      <SimpleReservationList
        reservations={mockReservations}
        searchTerm="+1234567891"
        onSearchChange={mockOnSearchChange}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.queryByTestId('reservation-res-1')).not.toBeInTheDocument()
    expect(screen.getByTestId('reservation-res-2')).toBeInTheDocument()
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
  })

  it('shows no results message when no matches', () => {
    render(
      <SimpleReservationList
        reservations={mockReservations}
        searchTerm="nonexistent"
        onSearchChange={mockOnSearchChange}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('no-results')).toBeInTheDocument()
    expect(screen.getByText('No reservations found')).toBeInTheDocument()
    expect(screen.queryByTestId('reservation-res-1')).not.toBeInTheDocument()
    expect(screen.queryByTestId('reservation-res-2')).not.toBeInTheDocument()
  })

  it('calls onSearchChange when search input changes', async () => {
    const user = userEvent.setup()
    
    render(
      <SimpleReservationList
        reservations={mockReservations}
        searchTerm=""
        onSearchChange={mockOnSearchChange}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    const searchInput = screen.getByTestId('search-input')
    await user.type(searchInput, 'john')

    expect(mockOnSearchChange).toHaveBeenCalledWith('j')
    expect(mockOnSearchChange).toHaveBeenCalledWith('o')
    expect(mockOnSearchChange).toHaveBeenCalledWith('h')
    expect(mockOnSearchChange).toHaveBeenCalledWith('n')
  })

  it('shows empty state when no reservations provided', () => {
    render(
      <SimpleReservationList
        reservations={[]}
        searchTerm=""
        onSearchChange={mockOnSearchChange}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByTestId('no-results')).toBeInTheDocument()
    expect(screen.getByText('No reservations found')).toBeInTheDocument()
  })
})
