'use client';

import React from 'react';
import Link from 'next/link';
import { ShoppingCart, Calendar, Star, AlertCircle, Users, Package, CreditCard, Gift } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Notification } from '@/lib/redux/api/endpoints/restaurant/notificationsApi';

// Utility function to get relative time
const getRelativeTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffInMinutes < 24 * 60) {
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInMinutes < 7 * 24 * 60) {
    const days = Math.floor(diffInMinutes / (24 * 60));
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead?: (id: string) => void;
  compact?: boolean;
}

export default function NotificationItem({
  notification,
  onMarkAsRead,
  compact = false
}: NotificationItemProps) {
  // Get the appropriate icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'order':
        return <ShoppingCart className="h-5 w-5 text-blue-500" />;
      case 'reservation':
        return <Calendar className="h-5 w-5 text-green-500" />;
      case 'review':
        return <Star className="h-5 w-5 text-yellow-500" />;
      case 'system':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'staff':
        return <Users className="h-5 w-5 text-purple-500" />;
      case 'inventory':
        return <Package className="h-5 w-5 text-orange-500" />;
      case 'payment':
        return <CreditCard className="h-5 w-5 text-indigo-500" />;
      case 'promotion':
        return <Gift className="h-5 w-5 text-pink-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  // Handle click to mark as read
  const handleClick = () => {
    if (!notification.isRead && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  // Get relative time (e.g., "2 hours ago")
  const relativeTime = getRelativeTime(notification.timestamp);

  const content = (
    <div
      className={cn(
        "flex items-start gap-3 p-3 transition-colors",
        !notification.isRead && "bg-[#f1edea]",
        notification.isRead ? "hover:bg-[#f9f7f5]" : "hover:bg-[#ece8e4]"
      )}
      onClick={handleClick}
    >
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-start">
          <h4 className={cn(
            "text-sm font-medium text-[#181510] line-clamp-1",
            !notification.isRead && "font-bold"
          )}>
            {notification.title}
          </h4>
          {!compact && (
            <span className="text-xs text-[#8a745c] ml-2 whitespace-nowrap">
              {relativeTime}
            </span>
          )}
        </div>
        <p className="text-xs text-[#8a745c] mt-1 line-clamp-2">
          {notification.message}
        </p>
        {compact && (
          <span className="text-xs text-[#8a745c] mt-1 block">
            {relativeTime}
          </span>
        )}
      </div>
      {!notification.isRead && (
        <div className="flex-shrink-0 w-2 h-2 rounded-full bg-[#e58219] mt-1.5" />
      )}
    </div>
  );

  // If there's a link, wrap the content in a Link component
  if (notification.link) {
    return (
      <Link href={notification.link} className="block">
        {content}
      </Link>
    );
  }

  // Otherwise, just return the content
  return content;
}
