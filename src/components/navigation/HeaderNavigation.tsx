'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { NavigationItem } from '@/lib/types/navigation';

interface HeaderNavigationProps {
  navItems?: NavigationItem[];
  showIcons?: boolean;
  autoHide?: boolean;
}

export default function HeaderNavigation({
  navItems = [],
  showIcons = true,
  autoHide = false
}: HeaderNavigationProps) {
  const pathname = usePathname();

  return (
    <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f1edea] px-10 py-3">
      <div className="flex items-center gap-4 text-[#181510]">
        <div className="size-4">
          <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
              fill="currentColor"
            ></path>
          </svg>
        </div>
        <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">Table Manager</h2>
      </div>
      <div className="flex flex-1 justify-end gap-8">
        <label className="flex flex-col min-w-40 !h-10 max-w-64">
          <div className="flex w-full flex-1 items-stretch rounded-lg h-full">
            <div
              className="text-[#8a745c] flex border-none bg-[#f1edea] items-center justify-center pl-4 rounded-l-lg border-r-0"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                ></path>
              </svg>
            </div>
            <input
              placeholder="Search"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-full placeholder:text-[#8a745c] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
              defaultValue=""
            />
          </div>
        </label>
        <div className="flex items-center gap-9">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`text-sm font-medium leading-normal text-[#181510] ${
                pathname === item.href ? 'font-bold' : ''
              } flex items-center gap-2`}
            >
              {showIcons && item.icon && (
                <span className="text-[#181510]">{item.icon}</span>
              )}
              {item.name}
            </Link>
          ))}
        </div>
        <button
          className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 bg-[#f1edea] text-[#181510] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
        >
          <div className="text-[#181510]">
            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
              ></path>
            </svg>
          </div>
        </button>
        <div
          className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
          style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuBTI0Z2xPUTyhRVeL1K9vLjQGbi-1UHxVlqwNVDF-iTr92vrWHqqemi73fpc8wOTxMsEWHp2sMWTYeuRGEAvkoprEmdcqQKjv1ysjZPz-Oxjj0nwmNth_9fIg4-n1G3JRRNt6U7R2hsx-S0tKSQkNScC_IoX_R82wZ86pqUnzHoPxbvYmN3_czqGASpoFbwwL2zK_CrjOxPpt8-qcbimmno5Mh8rjWKzJy07NeVJl7_QAhgvUVft3cXpPUn8wHTLxZSJXRyU2pkKbK3")'}}
        ></div>
      </div>
    </header>
  );
}
