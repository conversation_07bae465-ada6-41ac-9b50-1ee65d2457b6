'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { usePathname } from '@/i18n/navigation';
import { NavigationItem } from '@/lib/types/navigation';
import { cn } from '@/lib/utils';

interface BottomNavigationProps {
  navItems?: NavigationItem[];
  showIcons?: boolean;
  showLabels?: boolean;
  autoHide?: boolean;
}

export default function BottomNavigation({
  navItems = [],
  showIcons = true,
  showLabels = true,
  autoHide = false
}: BottomNavigationProps) {
  const pathname = usePathname();

  // Limit to 5 items for bottom navigation
  const limitedNavItems = navItems.slice(0, 5);

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-[#fbfaf9] border-t border-[#f1edea] z-50">
      <div className="flex justify-around items-center h-16">
        {limitedNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors',
              pathname === item.href
                ? 'text-[#181510] font-bold'
                : 'text-[#8a745c] hover:text-[#181510]'
            )}
          >
            {showIcons && item.icon && (
              <span className="text-current mb-1">
                {item.icon}
              </span>
            )}
            {showLabels && <span>{item.name}</span>}
          </Link>
        ))}
      </div>
    </div>
  );
}
