'use client';

import React from 'react';
import { format, parseISO } from 'date-fns';
import { StarRating } from '@/components/ui/star-rating';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, Flag, Archive, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { Review } from '@/lib/redux/api/endpoints/restaurant/restaurantApi';

interface ReviewCardProps {
  review: Review;
  showActions?: boolean;
  onRespond?: (reviewId: string) => void;
  onUpdateStatus?: (reviewId: string, status: string) => void;
}

export default function ReviewCard({ 
  review, 
  showActions = true,
  onRespond,
  onUpdateStatus
}: ReviewCardProps) {
  // Format the date
  const formattedDate = review.date ? format(parseISO(review.date), 'MMM d, yyyy') : '';
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Published</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'flagged':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Flagged</Badge>;
      case 'archived':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Archived</Badge>;
      default:
        return null;
    }
  };
  
  return (
    <Card className="bg-[#fbfaf9] border-[#e5e1dc] overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            {review.reviewer.avatar ? (
              <div 
                className="w-10 h-10 rounded-full bg-cover bg-center"
                style={{ backgroundImage: `url(${review.reviewer.avatar})` }}
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-[#e5e1dc] flex items-center justify-center text-[#8a745c] font-medium">
                {review.reviewer.name.charAt(0)}
              </div>
            )}
            <div>
              <div className="font-medium text-[#181510]">{review.reviewer.name}</div>
              <div className="text-xs text-[#8a745c]">{formattedDate}</div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StarRating rating={review.rating} size="sm" />
            <span className="text-sm font-medium text-[#181510]">{review.rating}</span>
            {getStatusBadge(review.status)}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pb-2">
        <p className="text-[#181510] text-sm">{review.content}</p>
        
        {review.reviewedItems && review.reviewedItems.length > 0 && (
          <div className="mt-3">
            <div className="text-xs font-medium text-[#8a745c] mb-1">Reviewed Items:</div>
            <div className="flex flex-wrap gap-1">
              {review.reviewedItems.map((item) => (
                <Badge 
                  key={item.id} 
                  variant="outline" 
                  className="text-xs bg-[#f1edea] border-[#e5e1dc] text-[#8a745c]"
                >
                  {item.name}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        {review.response && (
          <div className="mt-3 bg-[#f1edea] p-3 rounded-md">
            <div className="text-xs font-medium text-[#8a745c] mb-1">Your Response:</div>
            <p className="text-[#181510] text-sm">{review.response}</p>
          </div>
        )}
      </CardContent>
      
      {showActions && (
        <CardFooter className="pt-2 flex justify-end gap-2">
          {!review.response && (
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs border-[#e5e1dc]"
              onClick={() => onRespond?.(review.id)}
            >
              <MessageSquare className="h-3 w-3 mr-1" />
              Respond
            </Button>
          )}
          
          {review.status !== 'flagged' && (
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs border-[#e5e1dc]"
              onClick={() => onUpdateStatus?.(review.id, 'flagged')}
            >
              <Flag className="h-3 w-3 mr-1" />
              Flag
            </Button>
          )}
          
          {review.status !== 'archived' && (
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs border-[#e5e1dc]"
              onClick={() => onUpdateStatus?.(review.id, 'archived')}
            >
              <Archive className="h-3 w-3 mr-1" />
              Archive
            </Button>
          )}
          
          {review.status !== 'published' && (
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs border-[#e5e1dc]"
              onClick={() => onUpdateStatus?.(review.id, 'published')}
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Publish
            </Button>
          )}
          
          <Link href={`/app/restaurant/reviews/${review.id}`}>
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs border-[#e5e1dc] bg-[#e5ccb2] hover:bg-[#d6bd9e]"
            >
              View Details
            </Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  );
}
