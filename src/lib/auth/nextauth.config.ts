import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import GitH<PERSON><PERSON>rovider from "next-auth/providers/github"
import FacebookProvider from "next-auth/providers/facebook"
import Discord<PERSON><PERSON>ider from "next-auth/providers/discord"
import { supabase } from "@/lib/supabase/client"

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string
      role: string
    }
  }

  interface User {
    id: string
    email: string
    name?: string
    role: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    backendToken?: string
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    // OAuth Providers
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    }),
    DiscordProvider({
      clientId: process.env.DISCORD_CLIENT_ID!,
      clientSecret: process.env.DISCORD_CLIENT_SECRET!,
    }),
    // Credentials Provider
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required")
        }

        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password,
          })

          if (error) {
            throw new Error(error.message)
          }

          if (!data.user) {
            throw new Error("Invalid credentials")
          }

          return {
            id: data.user.id,
            email: data.user.email!,
            name: data.user.user_metadata?.name || data.user.email!,
            role: data.user.user_metadata?.role || 'user',
          }
        } catch (error) {
          console.error('Auth error:', error)
          throw error
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
    secret: process.env.NEXTAUTH_SECRET,
  },
  callbacks: {
    async signIn({ account }) {
      // Allow all OAuth sign-ins
      if (account?.provider !== "credentials") {
        return true
      }

      // For credentials, the authorize function handles validation
      return true
    },
    async jwt({ token, user, account, trigger, session }) {
      // Initial sign in
      if (user) {
        token.role = user.role || 'user'

        // For OAuth providers, we might need to create/update user in Supabase
        if (account?.provider !== "credentials") {
          try {
            // Check if user exists in Supabase or create them
            const { data: existingUser, error: fetchError } = await supabase
              .from('users')
              .select('*')
              .eq('email', user.email)
              .single()

            if (fetchError && fetchError.code !== 'PGRST116') {
              console.error('Error fetching user:', fetchError)
            }

            if (!existingUser) {
              // Create user in Supabase for OAuth sign-in
              const { data: newUser, error: createError } = await supabase
                .from('users')
                .insert({
                  id: user.id,
                  email: user.email,
                  name: user.name,
                  avatar_url: user.image,
                  provider: account?.provider || 'unknown',
                  role: 'user'
                })
                .select()
                .single()

              if (createError) {
                console.error('Error creating user:', createError)
                // Continue with default role even if user creation fails
                token.role = 'user'
              } else {
                token.role = newUser?.role || 'user'
              }
            } else {
              token.role = existingUser.role || 'user'
            }
          } catch (error) {
            console.error('OAuth user handling error:', error)
            token.role = 'user' // Default role
          }
        }
      }

      // Update session
      if (trigger === "update" && session) {
        token.role = session.role
      }

      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      // Default redirect to /app after successful authentication
      return `${baseUrl}/app`
    },
  },
  pages: {
    signIn: '/login',
    signOut: '/logout',
    error: '/auth/error',
  },
  events: {
    async signIn({ user }) {
      console.log('User signed in:', user.email)
    },
    async signOut({ token }) {
      console.log('User signed out:', token?.email)
    },
  },
  debug: process.env.NODE_ENV === 'development',
}
