/**
 * Enhanced Notifications API with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { apiSlice } from '../../apiSlice';

// Enhanced Notification interfaces
export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  type: 'order' | 'reservation' | 'review' | 'system' | 'staff' | 'inventory' | 'payment' | 'promotion';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  link?: string;
  actionLabel?: string;
  data?: Record<string, any>;
  shopId: string;
  branchId: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

// Enhanced filtering interface
export interface NotificationsFilters {
  // Pagination
  page: number;
  limit: number;
  
  // Sorting
  sort_by: 'timestamp' | 'title' | 'type' | 'priority' | 'isRead' | 'createdAt';
  sort_order: 'asc' | 'desc';
  
  // Filtering
  type?: 'order' | 'reservation' | 'review' | 'system' | 'staff' | 'inventory' | 'payment' | 'promotion';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  isRead?: boolean;
  search?: string;
  
  // Date filtering
  startDate?: string;
  endDate?: string;
  dateRange?: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year';
}

// Response interfaces
export interface NotificationsListResponse {
  data: Notification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  summary: {
    totalNotifications: number;
    unreadNotifications: number;
    readNotifications: number;
    urgentNotifications: number;
    highPriorityNotifications: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  };
}

// Request interfaces
export interface CreateNotificationRequest {
  title: string;
  message: string;
  type: 'order' | 'reservation' | 'review' | 'system' | 'staff' | 'inventory' | 'payment' | 'promotion';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  link?: string;
  actionLabel?: string;
  data?: Record<string, any>;
  userId?: string;
}

export interface UpdateNotificationRequest {
  title?: string;
  message?: string;
  isRead?: boolean;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  link?: string;
  actionLabel?: string;
  data?: Record<string, any>;
}

export interface BulkUpdateNotificationsRequest {
  notificationIds: string[];
  updates: {
    isRead?: boolean;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
  };
}

// Query parameters interface
export interface GetNotificationsParams {
  shopId: string;
  branchId: string;
  filters: NotificationsFilters;
}

// Enhanced Notifications API
export const notificationsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get notifications with backend-driven filtering, sorting, and pagination
    getNotifications: builder.query<NotificationsListResponse, GetNotificationsParams>({
      query: ({ shopId, branchId, filters }) => {
        const params = new URLSearchParams();
        
        // Pagination
        params.append('page', filters.page.toString());
        params.append('limit', filters.limit.toString());
        
        // Sorting
        params.append('sort_by', filters.sort_by);
        params.append('sort_order', filters.sort_order);
        
        // Filtering
        if (filters.type) params.append('type', filters.type);
        if (filters.priority) params.append('priority', filters.priority);
        if (filters.isRead !== undefined) params.append('isRead', filters.isRead.toString());
        if (filters.search) params.append('search', filters.search);
        
        // Date filtering
        if (filters.startDate) params.append('startDate', filters.startDate);
        if (filters.endDate) params.append('endDate', filters.endDate);
        if (filters.dateRange) params.append('dateRange', filters.dateRange);
        
        return {
          url: `notifications?shopId=${shopId}&branchId=${branchId}&${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        { type: 'Notifications', id: 'LIST' },
        ...(result?.data || []).map(({ id }) => ({ type: 'Notifications' as const, id })),
      ],
    }),

    // Get notification by ID
    getNotificationById: builder.query<Notification, { shopId: string; branchId: string; notificationId: string }>({
      query: ({ shopId, branchId, notificationId }) => ({
        url: `notifications/${notificationId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { notificationId }) => [
        { type: 'Notifications', id: notificationId },
      ],
    }),

    // Create notification
    createNotification: builder.mutation<Notification, { shopId: string; branchId: string; notification: CreateNotificationRequest }>({
      query: ({ shopId, branchId, notification }) => ({
        url: `notifications?shopId=${shopId}&branchId=${branchId}`,
        method: 'POST',
        body: notification,
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Update notification
    updateNotification: builder.mutation<Notification, { shopId: string; branchId: string; notificationId: string; updates: UpdateNotificationRequest }>({
      query: ({ shopId, branchId, notificationId, updates }) => ({
        url: `notifications/${notificationId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: (result, error, { notificationId }) => [
        { type: 'Notifications', id: notificationId },
        { type: 'Notifications', id: 'LIST' },
      ],
    }),

    // Bulk update notifications (mark as read/unread, change priority)
    bulkUpdateNotifications: builder.mutation<{ updated: number }, { shopId: string; branchId: string; request: BulkUpdateNotificationsRequest }>({
      query: ({ shopId, branchId, request }) => ({
        url: `notifications/bulk?shopId=${shopId}&branchId=${branchId}`,
        method: 'PATCH',
        body: request,
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Delete notification
    deleteNotification: builder.mutation<{ success: boolean }, { shopId: string; branchId: string; notificationId: string }>({
      query: ({ shopId, branchId, notificationId }) => ({
        url: `notifications/${notificationId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { notificationId }) => [
        { type: 'Notifications', id: notificationId },
        { type: 'Notifications', id: 'LIST' },
      ],
    }),

    // Mark all notifications as read
    markAllNotificationsAsRead: builder.mutation<{ updated: number }, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `notifications/mark-all-read?shopId=${shopId}&branchId=${branchId}`,
        method: 'POST',
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Clear all notifications
    clearAllNotifications: builder.mutation<{ deleted: number }, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `notifications/clear-all?shopId=${shopId}&branchId=${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Notifications', id: 'LIST' }],
    }),

    // Get notification statistics
    getNotificationStats: builder.query<NotificationsListResponse['summary'], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `notifications/stats?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: [{ type: 'Notifications', id: 'STATS' }],
    }),
  }),
});

// Export hooks
export const {
  useGetNotificationsQuery,
  useGetNotificationByIdQuery,
  useCreateNotificationMutation,
  useUpdateNotificationMutation,
  useBulkUpdateNotificationsMutation,
  useDeleteNotificationMutation,
  useMarkAllNotificationsAsReadMutation,
  useClearAllNotificationsMutation,
  useGetNotificationStatsQuery,
} = notificationsApi;

// Export types
export type {
  Notification,
  NotificationsFilters,
  NotificationsListResponse,
  CreateNotificationRequest,
  UpdateNotificationRequest,
  BulkUpdateNotificationsRequest,
  GetNotificationsParams,
};
