/**
 * RTK Query API endpoints for reviews management
 * Handles all review-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface Review {
  id: string;
  customerId?: string;
  customerName: string;
  customerEmail?: string;
  customerAvatar?: string;
  orderId?: string;
  rating: number;
  title?: string;
  comment: string;
  photos?: string[];
  source: 'google' | 'yelp' | 'facebook' | 'tripadvisor' | 'internal' | 'other';
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  isVerified: boolean;
  isPublic: boolean;
  response?: {
    message: string;
    respondedBy: string;
    respondedAt: string;
  };
  tags?: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  createdAt: string;
  updatedAt: string;
}

export interface ReviewFilters {
  rating?: number;
  source?: string;
  status?: string;
  sentiment?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  hasResponse?: boolean;
  page?: number;
  limit?: number;
}

export interface ReviewResponse {
  reviewId: string;
  message: string;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  responseRate: number;
  sentimentBreakdown: {
    positive: number;
    neutral: number;
    negative: number;
  };
  sourceBreakdown: Record<string, number>;
  recentTrend: {
    period: string;
    averageRating: number;
    totalReviews: number;
  }[];
}

export interface ReviewsListResponse {
  data: Review[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const reviewsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reviews with filters and pagination
    getReviews: builder.query<ReviewsListResponse, {
      merchantId: string;
      branchId: string;
      filters?: ReviewFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reviews?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Reviews',
        ...(result?.data || []).map(({ id }) => ({ type: 'Reviews' as const, id })),
      ],
    }),

    // Get single review
    getReview: builder.query<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ merchantId, branchId, reviewId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
      ],
    }),

    // Get review statistics
    getReviewStats: builder.query<ReviewStats, {
      merchantId: string;
      branchId: string;
      period?: string;
    }>({
      query: ({ merchantId, branchId, period = '30d' }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/stats?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['ReviewStats'],
    }),

    // Get recent reviews
    getRecentReviews: builder.query<Review[], {
      merchantId: string;
      branchId: string;
      limit?: number;
    }>({
      query: ({ merchantId, branchId, limit = 10 }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/recent?limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['Reviews'],
    }),

    // Get pending reviews
    getPendingReviews: builder.query<Review[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/pending`,
        method: 'GET',
      }),
      providesTags: ['Reviews'],
    }),

    // Respond to review
    respondToReview: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
      message: string;
    }>({
      query: ({ merchantId, branchId, reviewId, message }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/respond`,
        method: 'POST',
        body: { message },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Update review response
    updateReviewResponse: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
      message: string;
    }>({
      query: ({ merchantId, branchId, reviewId, message }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/respond`,
        method: 'PUT',
        body: { message },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
      ],
    }),

    // Delete review response
    deleteReviewResponse: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ merchantId, branchId, reviewId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/respond`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Update review status
    updateReviewStatus: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
      status: string;
    }>({
      query: ({ merchantId, branchId, reviewId, status }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Flag review
    flagReview: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
      reason: string;
    }>({
      query: ({ merchantId, branchId, reviewId, reason }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/flag`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
      ],
    }),

    // Hide review
    hideReview: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ merchantId, branchId, reviewId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/hide`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Show review
    showReview: builder.mutation<Review, {
      merchantId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ merchantId, branchId, reviewId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/${reviewId}/show`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Sync external reviews
    syncExternalReviews: builder.mutation<{ synced: number; errors: string[] }, {
      merchantId: string;
      branchId: string;
      sources?: string[];
    }>({
      query: ({ merchantId, branchId, sources = [] }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/sync`,
        method: 'POST',
        body: { sources },
      }),
      invalidatesTags: ['Reviews', 'ReviewStats'],
    }),

    // Export reviews
    exportReviews: builder.mutation<{ downloadUrl: string }, {
      merchantId: string;
      branchId: string;
      format: 'csv' | 'excel' | 'pdf';
      filters?: ReviewFilters;
    }>({
      query: ({ merchantId, branchId, format, filters = {} }) => {
        const params = new URLSearchParams();
        params.append('format', format);
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reviews/export?${params.toString()}`,
          method: 'POST',
        };
      },
    }),

    // Get review insights
    getReviewInsights: builder.query<{
      commonKeywords: Array<{ word: string; count: number; sentiment: string }>;
      improvementAreas: string[];
      strengths: string[];
      competitorComparison?: {
        averageRating: number;
        reviewCount: number;
        responseRate: number;
      };
    }, {
      merchantId: string;
      branchId: string;
      period?: string;
    }>({
      query: ({ merchantId, branchId, period = '30d' }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reviews/insights?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['ReviewStats'],
    }),
  }),
});

export const {
  useGetReviewsQuery,
  useGetReviewQuery,
  useGetReviewStatsQuery,
  useGetRecentReviewsQuery,
  useGetPendingReviewsQuery,
  useRespondToReviewMutation,
  useUpdateReviewResponseMutation,
  useDeleteReviewResponseMutation,
  useUpdateReviewStatusMutation,
  useFlagReviewMutation,
  useHideReviewMutation,
  useShowReviewMutation,
  useSyncExternalReviewsMutation,
  useExportReviewsMutation,
  useGetReviewInsightsQuery,
} = reviewsApi;
