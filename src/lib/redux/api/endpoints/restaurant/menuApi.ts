/**
 * RTK Query API endpoints for menu items
 * Handles all menu-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  price: number;
  categoryId: string;
  categoryName: string;
  image?: string;
  images?: string[];
  ingredients?: string[];
  allergens?: string[];
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sodium?: number;
  };
  preparationTime?: number;
  isAvailable: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpicy?: boolean;
  spiceLevel?: number;
  tags?: string[];
  options?: MenuItemOption[];
  createdAt: string;
  updatedAt: string;
}

export interface MenuItemOption {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  choices: MenuItemChoice[];
}

export interface MenuItemChoice {
  id: string;
  name: string;
  price: number;
  isDefault?: boolean;
}

export interface MenuCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  sortOrder: number;
  isActive: boolean;
  itemCount: number;
}

export interface MenuFilters {
  categoryId?: string;
  isAvailable?: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  search?: string;
  priceMin?: number;
  priceMax?: number;
  page?: number;
  limit?: number;
}

export interface CreateMenuItemRequest {
  name: string;
  description?: string;
  price: number;
  categoryId: string;
  image?: string;
  ingredients?: string[];
  allergens?: string[];
  nutritionalInfo?: MenuItem['nutritionalInfo'];
  preparationTime?: number;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpicy?: boolean;
  spiceLevel?: number;
  tags?: string[];
  options?: Omit<MenuItemOption, 'id'>[];
}

export interface UpdateMenuItemRequest {
  id: string;
  name?: string;
  description?: string;
  price?: number;
  categoryId?: string;
  image?: string;
  ingredients?: string[];
  allergens?: string[];
  nutritionalInfo?: MenuItem['nutritionalInfo'];
  preparationTime?: number;
  isAvailable?: boolean;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isSpicy?: boolean;
  spiceLevel?: number;
  tags?: string[];
  options?: MenuItemOption[];
}

export interface MenuItemsResponse {
  data: MenuItem[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const menuApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get menu items with filters and pagination
    getMenuItems: builder.query<MenuItemsResponse, {
      merchantId: string;
      branchId: string;
      filters?: MenuFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/menu/items?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'MenuItems',
        ...(result?.data || []).map(({ id }) => ({ type: 'MenuItems' as const, id })),
      ],
    }),

    // Get single menu item
    getMenuItem: builder.query<MenuItem, {
      merchantId: string;
      branchId: string;
      itemId: string;
    }>({
      query: ({ merchantId, branchId, itemId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { itemId }) => [
        { type: 'MenuItems', id: itemId },
      ],
    }),

    // Get menu item by slug
    getMenuItemBySlug: builder.query<MenuItem, {
      merchantId: string;
      branchId: string;
      slug: string;
    }>({
      query: ({ merchantId, branchId, slug }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items/slug/${slug}`,
        method: 'GET',
      }),
      providesTags: (result, error, { slug }) => [
        { type: 'MenuItems', id: slug },
      ],
    }),

    // Get menu categories
    getMenuCategories: builder.query<MenuCategory[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/categories`,
        method: 'GET',
      }),
      providesTags: ['MenuCategories'],
    }),

    // Create new menu item
    createMenuItem: builder.mutation<MenuItem, {
      merchantId: string;
      branchId: string;
      itemData: CreateMenuItemRequest;
    }>({
      query: ({ merchantId, branchId, itemData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items`,
        method: 'POST',
        body: itemData,
      }),
      invalidatesTags: ['MenuItems', 'MenuCategories'],
    }),

    // Update menu item
    updateMenuItem: builder.mutation<MenuItem, {
      merchantId: string;
      branchId: string;
      itemData: UpdateMenuItemRequest;
    }>({
      query: ({ merchantId, branchId, itemData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemData.id}`,
        method: 'PUT',
        body: itemData,
      }),
      invalidatesTags: (result, error, { itemData }) => [
        { type: 'MenuItems', id: itemData.id },
        'MenuItems',
        'MenuCategories',
      ],
    }),

    // Delete menu item
    deleteMenuItem: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      itemId: string;
    }>({
      query: ({ merchantId, branchId, itemId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { itemId }) => [
        { type: 'MenuItems', id: itemId },
        'MenuItems',
        'MenuCategories',
      ],
    }),

    // Toggle menu item availability
    toggleMenuItemAvailability: builder.mutation<MenuItem, {
      merchantId: string;
      branchId: string;
      itemId: string;
      isAvailable: boolean;
    }>({
      query: ({ merchantId, branchId, itemId, isAvailable }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}/availability`,
        method: 'PATCH',
        body: { isAvailable },
      }),
      invalidatesTags: (result, error, { itemId }) => [
        { type: 'MenuItems', id: itemId },
        'MenuItems',
      ],
    }),

    // Upload menu item image
    uploadMenuItemImage: builder.mutation<{ imageUrl: string }, {
      merchantId: string;
      branchId: string;
      itemId: string;
      file: File;
    }>({
      query: ({ merchantId, branchId, itemId, file }) => {
        const formData = new FormData();
        formData.append('image', file);
        
        return {
          url: `/merchants/${merchantId}/branches/${branchId}/menu/items/${itemId}/image`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { itemId }) => [
        { type: 'MenuItems', id: itemId },
      ],
    }),

    // Create menu category
    createMenuCategory: builder.mutation<MenuCategory, {
      merchantId: string;
      branchId: string;
      categoryData: {
        name: string;
        description?: string;
        image?: string;
        sortOrder?: number;
      };
    }>({
      query: ({ merchantId, branchId, categoryData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/categories`,
        method: 'POST',
        body: categoryData,
      }),
      invalidatesTags: ['MenuCategories'],
    }),

    // Update menu category
    updateMenuCategory: builder.mutation<MenuCategory, {
      merchantId: string;
      branchId: string;
      categoryId: string;
      categoryData: {
        name?: string;
        description?: string;
        image?: string;
        sortOrder?: number;
        isActive?: boolean;
      };
    }>({
      query: ({ merchantId, branchId, categoryId, categoryData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/categories/${categoryId}`,
        method: 'PUT',
        body: categoryData,
      }),
      invalidatesTags: ['MenuCategories'],
    }),

    // Delete menu category
    deleteMenuCategory: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      categoryId: string;
    }>({
      query: ({ merchantId, branchId, categoryId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/menu/categories/${categoryId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['MenuCategories', 'MenuItems'],
    }),
  }),
});

export const {
  useGetMenuItemsQuery,
  useGetMenuItemQuery,
  useGetMenuItemBySlugQuery,
  useGetMenuCategoriesQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
  useDeleteMenuItemMutation,
  useToggleMenuItemAvailabilityMutation,
  useUploadMenuItemImageMutation,
  useCreateMenuCategoryMutation,
  useUpdateMenuCategoryMutation,
  useDeleteMenuCategoryMutation,
} = menuApi;
