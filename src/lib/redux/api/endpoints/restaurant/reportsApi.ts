/**
 * RTK Query API endpoints for reports and analytics
 * Handles all report-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface SalesTrendData {
  period: string;
  sales: number;
  orders: number;
  date: string;
}

export interface PopularMenuItem {
  id: string;
  name: string;
  category: string;
  orders: number;
  revenue: number;
  image?: string;
  price: number;
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  averageSpend: number;
  repeatCustomerRate: number;
  customerLifetimeValue: number;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  revenueGrowth: number;
  averageOrderValue: number;
  totalOrders: number;
  orderGrowth: number;
}

export interface ReportsResponse {
  salesTrends: SalesTrendData[];
  popularItems: PopularMenuItem[];
  customerAnalytics: CustomerAnalytics;
  revenueAnalytics: RevenueAnalytics;
  period: string;
  generatedAt: string;
  // Pagination metadata
  total?: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

export interface ReportsListResponse {
  data: ReportsResponse[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  summary: {
    totalReports: number;
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    revenueGrowth: number;
    ordersGrowth: number;
  };
}

export interface ReportsFilters {
  // Pagination
  page?: number;
  limit?: number;

  // Sorting
  sort_by?: 'date' | 'revenue' | 'orders' | 'growth' | 'name' | 'category' | 'created_at';
  sort_order?: 'asc' | 'desc';

  // Filtering
  period?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  startDate?: string;
  endDate?: string;
  branchId?: string;
  category?: string;
  report_type?: 'sales' | 'customers' | 'staff' | 'tables' | 'popular-items';
  search?: string;

  // Date range shortcuts
  date_range?: 'today' | 'yesterday' | 'last_7_days' | 'last_30_days' | 'this_month' | 'last_month' | 'this_quarter' | 'last_quarter' | 'this_year' | 'last_year' | 'custom';
}

export interface DashboardStats {
  todayOrders: number;
  todayRevenue: number;
  todayReservations: number;
  activeStaff: number;
  ordersGrowth: number;
  revenueGrowth: number;
  reservationsGrowth: number;
}

export const reportsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Enhanced reports query with backend-driven filtering, sorting, and pagination
    getReports: builder.query<ReportsListResponse, {
      shopId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        // Add pagination parameters
        params.append('page', String(filters.page || 1));
        params.append('limit', String(filters.limit || 20));

        // Add sorting parameters
        params.append('sort_by', filters.sort_by || 'created_at');
        params.append('sort_order', filters.sort_order || 'desc');

        // Add filtering parameters
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '' && !['page', 'limit', 'sort_by', 'sort_order'].includes(key)) {
            params.append(key, String(value));
          }
        });

        return {
          url: `reports?shopId=${shopId}&branchId=${branchId}&${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: branchId },
      ],
    }),

    // Get comprehensive reports for a branch
    getBranchReports: builder.query<ReportsResponse, {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: branchId },
      ],
    }),

    // Get sales trends data
    getSalesTrends: builder.query<SalesTrendData[], {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/sales-trends?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-sales` },
      ],
    }),

    // Get popular menu items
    getPopularItems: builder.query<PopularMenuItem[], {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/popular-items?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-items` },
      ],
    }),

    // Get customer analytics
    getCustomerAnalytics: builder.query<CustomerAnalytics, {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/customer-analytics?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-customers` },
      ],
    }),

    // Get revenue analytics
    getRevenueAnalytics: builder.query<RevenueAnalytics, {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/revenue-analytics?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-revenue` },
      ],
    }),

    // Get dashboard statistics
    getDashboardStats: builder.query<DashboardStats, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/dashboard/stats`,
        method: 'GET',
      }),
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-dashboard` },
      ],
    }),

    // Export reports
    exportReports: builder.mutation<{ downloadUrl: string }, {
      merchantId: string;
      branchId: string;
      format: 'pdf' | 'excel' | 'csv';
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, format, filters = {} }) => {
        const params = new URLSearchParams();
        params.append('format', format);

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/export?${params.toString()}`,
          method: 'POST',
        };
      },
    }),

    // Get real-time metrics
    getRealTimeMetrics: builder.query<{
      activeOrders: number;
      todayRevenue: number;
      onlineCustomers: number;
      averageWaitTime: number;
    }, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/metrics/realtime`,
        method: 'GET',
      }),
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-realtime` },
      ],
    }),
  }),
});

export const {
  useGetReportsQuery,
  useGetBranchReportsQuery,
  useGetSalesTrendsQuery,
  useGetPopularItemsQuery,
  useGetCustomerAnalyticsQuery,
  useGetRevenueAnalyticsQuery,
  useGetDashboardStatsQuery,
  useExportReportsMutation,
  useGetRealTimeMetricsQuery,
} = reportsApi;
