/**
 * RTK Query API endpoints for reservations
 * Handles all reservation-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface Reservation {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  date: string;
  time: string;
  duration: number;
  tableId?: string;
  tableName?: string;
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no-show';
  specialRequests?: string;
  notes?: string;
  source: 'phone' | 'website' | 'walk-in' | 'app';
  createdAt: string;
  updatedAt: string;
}

export interface ReservationFilters {
  status?: string;
  date?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  tableId?: string;
  page?: number;
  limit?: number;
}

export interface CreateReservationRequest {
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  date: string;
  time: string;
  duration?: number;
  tableId?: string;
  specialRequests?: string;
  notes?: string;
  source?: string;
}

export interface UpdateReservationRequest {
  id: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  partySize?: number;
  date?: string;
  time?: string;
  duration?: number;
  tableId?: string;
  status?: string;
  specialRequests?: string;
  notes?: string;
}

export interface ReservationsResponse {
  data: Reservation[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface AvailableSlot {
  time: string;
  availableTables: number;
  suggestedTables: string[];
}

export const reservationsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reservations with filters and pagination (now uses shops API)
    getReservations: builder.query<ReservationsResponse, {
      shopId: string;
      branchId: string;
      filters?: ReservationFilters;
    }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/shops/${shopId}/branches/${branchId}/reservations?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Reservations',
        ...(result?.data || []).map(({ id }) => ({ type: 'Reservations' as const, id })),
      ],
    }),

    // Get single reservation (now uses shops API)
    getReservation: builder.query<Reservation, {
      shopId: string;
      branchId: string;
      reservationId: string;
    }>({
      query: ({ shopId, branchId, reservationId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
      ],
    }),

    // Get today's reservations (now uses shops API)
    getTodayReservations: builder.query<Reservation[], {
      shopId: string;
      branchId: string;
    }>({
      query: ({ shopId, branchId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/today`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),

    // Get available time slots (now uses shops API)
    getAvailableSlots: builder.query<AvailableSlot[], {
      shopId: string;
      branchId: string;
      date: string;
      partySize: number;
    }>({
      query: ({ shopId, branchId, date, partySize }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/availability?date=${date}&partySize=${partySize}`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),

    // Create new reservation (now uses shops API)
    createReservation: builder.mutation<Reservation, {
      shopId: string;
      branchId: string;
      reservationData: CreateReservationRequest;
    }>({
      query: ({ shopId, branchId, reservationData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations`,
        method: 'POST',
        body: reservationData,
      }),
      invalidatesTags: ['Reservations'],
    }),

    // Update reservation (now uses shops API)
    updateReservation: builder.mutation<Reservation, {
      shopId: string;
      branchId: string;
      reservationData: UpdateReservationRequest;
    }>({
      query: ({ shopId, branchId, reservationData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationData.id}`,
        method: 'PUT',
        body: reservationData,
      }),
      invalidatesTags: (result, error, { reservationData }) => [
        { type: 'Reservations', id: reservationData.id },
        'Reservations',
      ],
    }),

    // Update reservation status (now uses shops API)
    updateReservationStatus: builder.mutation<Reservation, {
      shopId: string;
      branchId: string;
      reservationId: string;
      status: string;
    }>({
      query: ({ shopId, branchId, reservationId, status }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Cancel reservation (now uses shops API)
    cancelReservation: builder.mutation<void, {
      shopId: string;
      branchId: string;
      reservationId: string;
      reason?: string;
    }>({
      query: ({ shopId, branchId, reservationId, reason }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationId}/cancel`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Check in reservation (now uses shops API)
    checkInReservation: builder.mutation<Reservation, {
      shopId: string;
      branchId: string;
      reservationId: string;
      tableId?: string;
    }>({
      query: ({ shopId, branchId, reservationId, tableId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationId}/checkin`,
        method: 'POST',
        body: { tableId },
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Mark as no-show (now uses shops API)
    markNoShow: builder.mutation<Reservation, {
      shopId: string;
      branchId: string;
      reservationId: string;
    }>({
      query: ({ shopId, branchId, reservationId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationId}/no-show`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Send confirmation (now uses shops API)
    sendConfirmation: builder.mutation<{ success: boolean }, {
      shopId: string;
      branchId: string;
      reservationId: string;
      method: 'email' | 'sms';
    }>({
      query: ({ shopId, branchId, reservationId, method }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/${reservationId}/confirm`,
        method: 'POST',
        body: { method },
      }),
    }),

    // Get reservation statistics (now uses shops API)
    getReservationStats: builder.query<{
      totalReservations: number;
      todayReservations: number;
      upcomingReservations: number;
      completedReservations: number;
      cancelledReservations: number;
      noShowRate: number;
    }, {
      shopId: string;
      branchId: string;
      period?: string;
    }>({
      query: ({ shopId, branchId, period = '30d' }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reservations/stats?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),
  }),
});

export const {
  useGetReservationsQuery,
  useGetReservationQuery,
  useGetTodayReservationsQuery,
  useGetAvailableSlotsQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useUpdateReservationStatusMutation,
  useCancelReservationMutation,
  useCheckInReservationMutation,
  useMarkNoShowMutation,
  useSendConfirmationMutation,
  useGetReservationStatsQuery,
} = reservationsApi;
