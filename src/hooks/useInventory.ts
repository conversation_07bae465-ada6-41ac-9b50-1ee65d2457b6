/**
 * Enhanced useInventory Hook with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetInventoryItemsQuery,
  useCreateInventoryItemMutation,
  useUpdateInventoryItemMutation,
  useDeleteInventoryItemMutation,
  useAdjustStockMutation,
  useBulkUpdateStockMutation,
  useGetInventoryStatsQuery,
  useGetLowStockAlertsQuery,
  useGetExpiringItemsQuery,
  type InventoryFilters,
  type InventoryItem,
  type CreateInventoryItemRequest,
  type UpdateInventoryItemRequest,
  type StockAdjustmentRequest,
} from '@/lib/redux/api/endpoints/restaurant/inventoryApi';

// Hook options interface
export interface UseInventoryOptions {
  shopId: string;
  branchId: string;
  initialFilters?: Partial<InventoryFilters>;
}

// Enhanced useInventory hook
export function useInventory({
  shopId,
  branchId,
  initialFilters = {}
}: UseInventoryOptions) {
  const [filters, setFilters] = useState<InventoryFilters>({
    page: 1,
    limit: 20,
    sort_by: 'name',
    sort_order: 'asc',
    ...initialFilters
  });

  // API queries
  const {
    data: inventoryData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetInventoryItemsQuery({
    shopId,
    branchId,
    filters
  });

  const {
    data: statsData,
    isLoading: isStatsLoading
  } = useGetInventoryStatsQuery({ shopId, branchId });

  const {
    data: lowStockItems,
    isLoading: isLowStockLoading
  } = useGetLowStockAlertsQuery({ shopId, branchId });

  const {
    data: expiringItems,
    isLoading: isExpiringLoading
  } = useGetExpiringItemsQuery({ shopId, branchId, days: 7 });

  // Mutations
  const [createItem, { isLoading: isCreating }] = useCreateInventoryItemMutation();
  const [updateItem, { isLoading: isUpdating }] = useUpdateInventoryItemMutation();
  const [deleteItem, { isLoading: isDeleting }] = useDeleteInventoryItemMutation();
  const [adjustStock, { isLoading: isAdjusting }] = useAdjustStockMutation();
  const [bulkUpdateStock, { isLoading: isBulkUpdating }] = useBulkUpdateStockMutation();

  // Data processing
  const items = inventoryData?.data || [];
  const pagination = inventoryData ? {
    currentPage: inventoryData.page,
    totalPages: inventoryData.totalPages,
    totalItems: inventoryData.total,
    itemsPerPage: inventoryData.limit,
    hasNextPage: inventoryData.page < inventoryData.totalPages,
    hasPreviousPage: inventoryData.page > 1,
  } : undefined;

  // Statistics
  const stats = useMemo(() => {
    const summary = inventoryData?.summary || statsData;
    return {
      totalItems: summary?.totalItems || 0,
      lowStockItems: summary?.lowStockItems || 0,
      outOfStockItems: summary?.outOfStockItems || 0,
      expiringSoonItems: summary?.expiringSoonItems || 0,
      totalValue: summary?.totalValue || 0,
      byCategory: summary?.byCategory || {},
      byStatus: summary?.byStatus || {},
      bySupplier: summary?.bySupplier || {},
      lowStockPercentage: summary?.totalItems ? 
        Math.round((summary.lowStockItems / summary.totalItems) * 100) : 0,
      outOfStockPercentage: summary?.totalItems ? 
        Math.round((summary.outOfStockItems / summary.totalItems) * 100) : 0,
    };
  }, [inventoryData?.summary, statsData]);

  // Filter management functions
  const updateFilters = useCallback((newFilters: Partial<InventoryFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 20,
      sort_by: 'name',
      sort_order: 'asc',
    });
  }, []);

  // Search function
  const handleSearch = useCallback((searchTerm: string) => {
    updateFilters({ search: searchTerm, page: 1 });
  }, [updateFilters]);

  // Filter functions
  const handleCategoryFilter = useCallback((category: string | undefined) => {
    updateFilters({ category, page: 1 });
  }, [updateFilters]);

  const handleStatusFilter = useCallback((status: InventoryFilters['status']) => {
    updateFilters({ status, page: 1 });
  }, [updateFilters]);

  const handleSupplierFilter = useCallback((supplier: string | undefined) => {
    updateFilters({ supplier, page: 1 });
  }, [updateFilters]);

  const handleStockLevelFilter = useCallback((filterType: 'lowStock' | 'outOfStock' | 'expiringSoon', enabled: boolean) => {
    updateFilters({ [filterType]: enabled ? true : undefined, page: 1 });
  }, [updateFilters]);

  // Sorting function
  const handleSort = useCallback((sort_by: InventoryFilters['sort_by'], sort_order?: InventoryFilters['sort_order']) => {
    const newSortOrder = sort_order || (filters.sort_by === sort_by && filters.sort_order === 'asc' ? 'desc' : 'asc');
    updateFilters({ sort_by, sort_order: newSortOrder, page: 1 });
  }, [filters.sort_by, filters.sort_order, updateFilters]);

  // Pagination functions
  const handlePageChange = useCallback((page: number) => {
    updateFilters({ page });
  }, [updateFilters]);

  const handleLimitChange = useCallback((limit: number) => {
    updateFilters({ limit, page: 1 });
  }, [updateFilters]);

  // Inventory item actions
  const handleCreateItem = useCallback(async (itemData: CreateInventoryItemRequest) => {
    try {
      const result = await createItem({
        shopId,
        branchId,
        item: itemData
      }).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to create inventory item:', error);
      throw error;
    }
  }, [createItem, shopId, branchId]);

  const handleUpdateItem = useCallback(async (itemId: string, updates: UpdateInventoryItemRequest) => {
    try {
      const result = await updateItem({
        shopId,
        branchId,
        itemId,
        updates
      }).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to update inventory item:', error);
      throw error;
    }
  }, [updateItem, shopId, branchId]);

  const handleDeleteItem = useCallback(async (itemId: string) => {
    try {
      await deleteItem({
        shopId,
        branchId,
        itemId
      }).unwrap();
    } catch (error) {
      console.error('Failed to delete inventory item:', error);
      throw error;
    }
  }, [deleteItem, shopId, branchId]);

  const handleStockAdjustment = useCallback(async (adjustment: StockAdjustmentRequest) => {
    try {
      const result = await adjustStock({
        shopId,
        branchId,
        adjustment
      }).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to adjust stock:', error);
      throw error;
    }
  }, [adjustStock, shopId, branchId]);

  // Convenience functions
  const getItemById = useCallback((id: string): InventoryItem | undefined => {
    return items.find(item => item.id === id);
  }, [items]);

  const getItemsByCategory = useCallback((category: string): InventoryItem[] => {
    return items.filter(item => item.category === category);
  }, [items]);

  const getItemsByStatus = useCallback((status: string): InventoryItem[] => {
    return items.filter(item => item.status === status);
  }, [items]);

  const getItemsBySupplier = useCallback((supplier: string): InventoryItem[] => {
    return items.filter(item => item.supplier === supplier);
  }, [items]);

  const getLowStockItems = useCallback((): InventoryItem[] => {
    return items.filter(item => item.status === 'low-stock');
  }, [items]);

  const getOutOfStockItems = useCallback((): InventoryItem[] => {
    return items.filter(item => item.status === 'out-of-stock');
  }, [items]);

  const getExpiringItems = useCallback((): InventoryItem[] => {
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    
    return items.filter(item => {
      if (!item.expiryDate) return false;
      const expiryDate = new Date(item.expiryDate);
      return expiryDate <= threeDaysFromNow && expiryDate > new Date();
    });
  }, [items]);

  // Utility functions
  const formatCurrency = useCallback((amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }, []);

  const formatDate = useCallback((dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  }, []);

  const getStatusColor = useCallback((status: string): string => {
    switch (status) {
      case 'in-stock': return 'text-green-600 bg-green-50 border-green-200';
      case 'low-stock': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'out-of-stock': return 'text-red-600 bg-red-50 border-red-200';
      case 'expired': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }, []);

  const isExpiringSoon = useCallback((expiryDate?: string): boolean => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    return expiry <= threeDaysFromNow && expiry > new Date();
  }, []);

  return {
    // Data
    items,
    totalCount: inventoryData?.total || 0,
    currentPage: filters.page,
    totalPages: pagination?.totalPages || 0,
    filters,
    stats,
    pagination,
    lowStockItems: lowStockItems || [],
    expiringItems: expiringItems || [],

    // Loading states
    isLoading,
    isStatsLoading,
    isLowStockLoading,
    isExpiringLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isAdjusting,
    isBulkUpdating,
    isError,
    error,

    // Filter management
    updateFilters,
    resetFilters,
    handleSearch,
    handleCategoryFilter,
    handleStatusFilter,
    handleSupplierFilter,
    handleStockLevelFilter,
    handleSort,
    handlePageChange,
    handleLimitChange,

    // Inventory actions
    handleCreateItem,
    handleUpdateItem,
    handleDeleteItem,
    handleStockAdjustment,

    // Convenience functions
    getItemById,
    getItemsByCategory,
    getItemsByStatus,
    getItemsBySupplier,
    getLowStockItems,
    getOutOfStockItems,
    getExpiringItems,

    // Utility functions
    formatCurrency,
    formatDate,
    getStatusColor,
    isExpiringSoon,
    refetch,
  };
}
